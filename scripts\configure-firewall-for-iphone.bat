@echo off
REM Windows Firewall Configuration for iPhone Testing
REM Run this as Administrator to allow iPhone connections

echo 🔥 Configuring Windows Firewall for iPhone Testing...

REM Allow React Native Metro bundler
netsh advfirewall firewall add rule name="React Native Metro (iPhone)" dir=in action=allow protocol=TCP localport=8081

REM Allow CCALC Backend
netsh advfirewall firewall add rule name="CCALC Backend (iPhone)" dir=in action=allow protocol=TCP localport=3000

REM Allow CCALC Frontend  
netsh advfirewall firewall add rule name="CCALC Frontend (iPhone)" dir=in action=allow protocol=TCP localport=3001

REM Allow Expo CLI
netsh advfirewall firewall add rule name="Expo CLI (iPhone)" dir=in action=allow protocol=TCP localport=19000
netsh advfirewall firewall add rule name="Expo CLI Tunnel (iPhone)" dir=in action=allow protocol=TCP localport=19001

echo ✅ Firewall rules added successfully!
echo 📱 Your iPhone should now be able to connect to:
echo    - Backend: http://*************:3000
echo    - Frontend: http://*************:3001  
echo    - Metro: http://*************:8081
echo    - Expo: http://*************:19000
echo.
echo 🔍 Test from iPhone Safari: http://*************:3000/health
pause
