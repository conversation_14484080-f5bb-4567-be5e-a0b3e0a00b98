import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import UserModel from '../../models/User';
import crypto from 'crypto';
import { logSecurityEvent, logLoginAttempt } from '../../utils/security-logger';
import { generateCsrfToken } from '../../utils/csrf-protection';

/**
 * Expression-only authentication controller
 * For mobile app calculator authentication - finds user by expression match
 * POST /api/auth/expression
 */
export async function expressionAuthController(req: Request, res: Response): Promise<void> {
  try {
    const { expression, deviceFingerprint, bleUUID } = req.body as {
      expression: string;
      deviceFingerprint?: string;
      bleUUID?: string;
    };

    // Validate expression is provided
    if (!expression || expression.trim().length === 0) {
      res.status(400).json({ error: 'Expression is required' });
      return;
    }

    console.log('🧮 Expression authentication attempt:', {
      expression: expression.substring(0, 20) + '...',
      hasDeviceFingerprint: !!deviceFingerprint,
      hasBleUUID: !!bleUUID
    });

    // Find all active users and try to match expression
    const users = await UserModel.find({
      status: { $in: ['active', 'pending_device_registration'] }
    });

    let matchedUser = null;

    // Try to find user with matching expression
    for (const user of users) {
      try {
        const isMatch = await bcrypt.compare(expression, user.expressionHash);
        if (isMatch) {
          matchedUser = user;
          break;
        }
      } catch (error) {
        console.error('Expression comparison error:', error);
      }
    }

    if (!matchedUser) {
      // Log failed attempt
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'user',
        username: 'unknown',
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'] || 'unknown',
        success: false,
        reason: 'Invalid expression - no matching user found',
        details: {
          expressionLength: expression.length,
          hasDeviceFingerprint: !!deviceFingerprint
        }
      });

      res.status(401).json({ error: 'Authentication failed' });
      return;
    }

    console.log(`✅ Expression matched for user: ${matchedUser.username}`);

    // Check if this is first login (no device registered) or subsequent login
    const isFirstLogin = matchedUser.status === 'pending_device_registration' ||
      !matchedUser.deviceFingerprintHash;

    if (isFirstLogin) {
      // First time login - just expression is enough
      if (deviceFingerprint && bleUUID) {
        // Register device if provided
        console.log('📱 Registering device for first login');

        const fingerprintHash = crypto.createHash('sha256')
          .update(deviceFingerprint)
          .digest('hex');

        const bleUUIDHash = crypto.createHash('sha256')
          .update(bleUUID)
          .digest('hex');

        matchedUser.deviceFingerprintHash = fingerprintHash;
        matchedUser.bleUUIDHash = bleUUIDHash;
        matchedUser.status = 'active';
        // Parse detailed device information from fingerprint
        let deviceDetails: any = {};
        try {
          const parsedFingerprint = JSON.parse(deviceFingerprint);
          deviceDetails = {
            deviceId: parsedFingerprint.deviceId || 'unknown',
            deviceName: parsedFingerprint.deviceName || parsedFingerprint.hardware?.modelName || 'Unknown Device',
            operatingSystem: parsedFingerprint.platform || parsedFingerprint.hardware?.brand || 'Unknown OS',
            deviceModel: parsedFingerprint.hardware?.modelName || 'Unknown Model',
            manufacturer: parsedFingerprint.hardware?.manufacturer || 'Unknown',
            brand: parsedFingerprint.hardware?.brand || 'Unknown',
            screenResolution: parsedFingerprint.screen ? `${parsedFingerprint.screen.width}x${parsedFingerprint.screen.height}` : 'Unknown',
            totalMemory: parsedFingerprint.hardware?.totalMemory || 0,
            deviceType: parsedFingerprint.hardware?.deviceType || 'Unknown'
          };
        } catch (error) {
          console.warn('Failed to parse device fingerprint details:', error);
          deviceDetails = {
            deviceId: 'unknown',
            deviceName: req.headers['x-device-model'] as string || 'Unknown Device',
            operatingSystem: req.headers['x-device-os'] as string || 'Unknown OS',
            deviceModel: 'Unknown Model',
            manufacturer: 'Unknown',
            brand: 'Unknown',
            screenResolution: 'Unknown',
            totalMemory: 0,
            deviceType: 'Unknown'
          };
        }

        matchedUser.deviceMetadata = {
          registeredAt: new Date(),
          model: deviceDetails.deviceModel,
          os: deviceDetails.operatingSystem,
          // Store detailed device information for admin panel
          deviceId: deviceDetails.deviceId,
          deviceName: deviceDetails.deviceName,
          manufacturer: deviceDetails.manufacturer,
          brand: deviceDetails.brand,
          screenResolution: deviceDetails.screenResolution,
          totalMemory: deviceDetails.totalMemory,
          deviceType: deviceDetails.deviceType
        };

        await matchedUser.save();

        logSecurityEvent({
          eventType: 'device_registration',
          userType: 'user',
          username: matchedUser.username,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'] || 'unknown',
          success: true,
          reason: 'First login device registration',
          details: {
            deviceModel: req.headers['x-device-model'] || 'Unknown',
            deviceOS: req.headers['x-device-os'] || 'Unknown',
            registrationTime: new Date().toISOString()
          }
        });
      }
    } else {
      // Subsequent login - verify device fingerprint
      if (!deviceFingerprint || !bleUUID) {
        res.status(400).json({
          error: 'Device fingerprint and BLE UUID required for registered user'
        });
        return;
      }

      const fingerprintHash = crypto.createHash('sha256')
        .update(deviceFingerprint)
        .digest('hex');

      const bleUUIDHash = crypto.createHash('sha256')
        .update(bleUUID)
        .digest('hex');

      if (matchedUser.deviceFingerprintHash !== fingerprintHash ||
        matchedUser.bleUUIDHash !== bleUUIDHash) {

        logSecurityEvent({
          eventType: 'failed_login',
          userType: 'user',
          username: matchedUser.username,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'] || 'unknown',
          success: false,
          reason: 'Device fingerprint mismatch',
          details: { userId: (matchedUser as any)._id.toString() }
        });

        res.status(401).json({ error: 'Device not recognized' });
        return;
      }
    }

    // Update last login
    matchedUser.lastLoginAt = new Date();
    matchedUser.failedLoginAttempts = 0;
    await matchedUser.save();

    // Generate JWT token
    const tokenPayload = {
      userId: (matchedUser as any)._id.toString(),
      username: matchedUser.username,
      type: 'user',
      fingerprint: deviceFingerprint ? crypto.createHash('sha256').update(deviceFingerprint).digest('hex').substring(0, 16) : null,
      bleUUID: bleUUID ? crypto.createHash('sha256').update(bleUUID).digest('hex').substring(0, 16) : null,
    };

    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET!, {
      expiresIn: '24h',
      issuer: 'ccalc-expression-auth',
      audience: 'ccalc-mobile-app'
    });

    // Generate CSRF token
    const csrfToken = generateCsrfToken((matchedUser as any)._id.toString());

    // Log successful login
    logSecurityEvent({
      eventType: 'login',
      userType: 'user',
      username: matchedUser.username,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'] || 'unknown',
      success: true,
      userId: (matchedUser as any)._id.toString()
    });

    console.log('🎉 Expression authentication successful');

    res.status(200).json({
      token,
      csrfToken,
      isFirstLogin,
      user: {
        id: (matchedUser as any)._id.toString(),
        username: matchedUser.username,
        displayName: matchedUser.profile.displayName,
        status: matchedUser.status,
        deviceRegistered: !!matchedUser.deviceFingerprintHash
      }
    });

  } catch (error) {
    console.error('Expression authentication error:', error);

    logSecurityEvent({
      eventType: 'failed_login',
      userType: 'user',
      username: 'unknown',
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'] || 'unknown',
      success: false,
      reason: 'Server error during expression authentication',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });

    res.status(500).json({ error: 'Authentication system error' });
  }
}
