/**
 * Chat Service
 * Handles messaging functionality for mobile app
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from './AuthService';
import { MediaAttachment } from './MediaService';


export interface ChatMessage {
  id: string;
  chatId: string;
  text: string;
  sender: 'user' | 'superuser';
  timestamp: number;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  isDelivered?: boolean;
  isRead?: boolean;
  attachment?: MediaAttachment;
  serverId?: string; // Server-assigned ID (different from client-side ID)
  clientId?: string; // Unique client-side ID that never changes
}

export interface ChatStatus {
  isConnected: boolean;
  superuserOnline: boolean;
  lastActivity?: number;
}

export interface SendMessageResult {
  success: boolean;
  message?: ChatMessage;
  error?: string;
}

export interface GetMessagesResult {
  success: boolean;
  messages?: ChatMessage[];
  error?: string;
}

export class ChatService {
  private static instance: ChatService;
  private authService: AuthService;
  private backendUrl: string;

  private constructor() {
    this.authService = AuthService.getInstance();
    this.backendUrl = this.authService.getConfig().backendUrl;
  }

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * Safely parse timestamp from various formats
   */
  private parseTimestamp(timestamp: any): number {
    if (!timestamp) {
      console.warn('⚠️ Missing timestamp, using current time');
      return Date.now();
    }

    // If it's already a number, return it
    if (typeof timestamp === 'number') {
      return timestamp;
    }

    // Try to parse as date
    const parsed = new Date(timestamp);
    if (isNaN(parsed.getTime())) {
      console.warn('⚠️ Invalid timestamp format:', timestamp, 'using current time');
      return Date.now();
    }

    return parsed.getTime();
  }

  /**
   * Send message to superuser
   */
  public async sendMessage(message: string, attachment?: MediaAttachment): Promise<SendMessageResult> {
    try {
      const logMessage = attachment 
        ? `📤 Sending message with attachment: ${attachment.name} (${Math.round(attachment.size / 1024)}KB)` 
        : `📤 Sending message: ${message.substring(0, 50)}...`;
      console.log(logMessage);

      const token = this.authService.getToken();
      if (!token) {
        console.error('❌ No authentication token available');
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      // Send message as plain text
      let messageContent = message.trim();
      
      // Fix for media-only messages to satisfy backend validation requirement
      if ((!messageContent || messageContent === '') && attachment) {
        if (attachment.isImage) {
          messageContent = '📷 Image';
        } else if (attachment.isVideo) {
          messageContent = '🎥 Video';
        } else if (attachment.isAudio) {
          messageContent = '🎵 Audio';
        } else {
          messageContent = '📎 File';
        }
        console.log('⚠️ Empty message with attachment, using default text:', messageContent);
      }

      let requestBody;
      let headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`,
      };

      if (attachment) {
        console.log('📤 Sending message with attachment:', attachment.name, `(${Math.round(attachment.size/1024)}KB)`);
        
        // Send as multipart/form-data for file upload
        const formData = new FormData();
        formData.append('message', messageContent);
        formData.append('timestamp', Date.now().toString());
        formData.append('isEncrypted', 'false');
        
        // Enhanced file upload format with proper MIME type detection
        const fileToUpload = {
          uri: attachment.uri,
          name: attachment.name,
          type: attachment.mimeType || 'application/octet-stream',
        };

        // Debug log the file details
        console.log('📤 File details:', {
          name: fileToUpload.name,
          type: fileToUpload.type,
          uri: attachment.uri.substring(0, 80) + (attachment.uri.length > 80 ? '...' : ''),
          size: attachment.size,
          isImage: attachment.isImage,
          isVideo: attachment.isVideo,
          hasId: !!attachment.id
        });

        // Validate file before upload
        if (!attachment.uri || attachment.uri.trim() === '') {
          throw new Error('Invalid file URI');
        }
        
        if (!attachment.name || attachment.name.trim() === '') {
          throw new Error('Invalid file name');
        }
        
        if (attachment.size <= 0) {
          console.warn('⚠️ File size is 0 or negative, this may cause upload issues');
        }

        // In React Native, we need to cast the file object for FormData
        formData.append('attachment', fileToUpload as any);

        requestBody = formData;
        // Let React Native set the correct Content-Type for multipart/form-data
        // Remove Content-Type header to let fetch set it automatically with boundary
        delete headers['Content-Type'];
      } else {
        // Send as JSON for text-only message
        headers['Content-Type'] = 'application/json';
        requestBody = JSON.stringify({
          message: messageContent,
          timestamp: Date.now(),
          isEncrypted: false,
        });
      }

      const response = await fetch(`${this.backendUrl}/api/chat/unified/send`, {
        method: 'POST',
        headers,
        body: requestBody,
      });

      console.log('📡 Response status:', response.status);

      const responseText = await response.text();
      console.log('📡 Response body (first 500 chars):', responseText.substring(0, 500));

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse JSON response:', parseError);
        console.log('📡 Raw response:', responseText);
        throw new Error(`Invalid JSON response: ${response.status}`);
      }

      console.log('📡 Parsed response data:', data);

      if (!response.ok) {
        console.error('❌ Request failed:', response.status, data);
        throw new Error(data.error || `HTTP ${response.status}: Failed to send message`);
      }

      if (data.success) {
        const chatMessage: ChatMessage = {
          id: data.message.id,
          chatId: data.message.chatId,
          text: message.trim(),
          sender: 'user',
          timestamp: data.message.timestamp,
          status: 'sent',
          isDelivered: false,
          isRead: false,
          attachment: attachment,
        };

        console.log('✅ Message sent successfully:', chatMessage.id);
        return {
          success: true,
          message: chatMessage,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Send message error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat messages from backend
   */  public async getMessages(limit: number = 50, offset: number = 0): Promise<GetMessagesResult> {
    try {
      console.log('📥 Getting messages...');

      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(
        `${this.backendUrl}/api/chat/unified/messages?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get messages');
      }

      if (data.success) {
        const deviceFingerprint = await this.authService.getDeviceFingerprint();
        const userId = 'mobileuser'; // Fixed mobile user ID for consistency
        
        const messages: ChatMessage[] = await Promise.all((data.messages || []).map(async (msg: any) => {
          // Parse attachment if present (updated for unified chat system)
          let attachment: MediaAttachment | undefined;
          if (msg.content?.attachment || msg.mediaRecord) {
            const att = msg.content?.attachment;
            const media = msg.mediaRecord;

            // Prefer mediaRecord (from unified system) over legacy attachment
            if (media) {
              attachment = {
                id: media.mediaId,
                name: media.originalName,
                type: this.getFileCategory(media.mimeType || ''),
                size: media.size || 0,
                uri: `${this.backendUrl}/api/chat/unified/media/${media.mediaId}`,
                mimeType: media.mimeType,
                thumbnailUri: media.thumbnailPath ? `${this.backendUrl}/api/chat/unified/media/${media.mediaId}?thumbnail=true` : undefined
              };
            } else if (att) {
              // Legacy attachment format
              attachment = {
                id: `attachment_${msg.messageId}`,
                name: att.originalName || att.fileName || 'Unknown file',
                type: this.getFileCategory(att.mimeType || ''),
                size: att.size || 0,
                uri: att.filePath ? `${this.backendUrl}/api/chat/mobile/attachment/${att.fileName}` : '',
                mimeType: att.mimeType
              };
            }
          }

          // Use message text as-is (no encryption/decryption)
          let messageText = msg.content?.text || msg.text || '';

          return {
            id: msg.messageId || msg.id,
            chatId: msg.chatId,
            text: messageText,
            sender: msg.senderId === data.currentUserId ? 'user' : 'superuser',
            timestamp: this.parseTimestamp(msg.metadata?.sentAt || msg.sentAt || msg.timestamp),
            status: msg.status || 'delivered',
            isDelivered: true,
            isRead: msg.readAt ? true : false,
            attachment
          };
        }));

        console.log('✅ Retrieved messages:', messages.length);
        return {
          success: true,
          messages,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Get messages error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat status (connection, superuser online status)
   */  public async getChatStatus(): Promise<ChatStatus> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          isConnected: false,
          superuserOnline: false,
        };
      }

      const response = await fetch(`${this.backendUrl}/api/chat/unified/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return {
          isConnected: true,
          superuserOnline: data.status?.superuserOnline || false,
          lastActivity: data.status?.lastActivity ? new Date(data.status.lastActivity).getTime() : undefined,
        };
      }

      return {
        isConnected: false,
        superuserOnline: false,
      };

    } catch (error) {
      console.error('Get chat status error:', error);
      return {
        isConnected: false,
        superuserOnline: false,
      };
    }
  }

  /**
   * Clear local chat data (on logout)
   */
  public async clearChatData(): Promise<void> {
    try {
      await AsyncStorage.removeItem('chat_messages');
      await AsyncStorage.removeItem('chat_drafts');
      console.log('Chat data cleared');
    } catch (error) {
      console.error('Failed to clear chat data:', error);
    }
  }

  /**
   * Cache messages locally for offline access
   */
  private async cacheMessages(messages: ChatMessage[]): Promise<void> {
    try {
      await AsyncStorage.setItem('chat_messages', JSON.stringify(messages));
    } catch (error) {
      console.error('Failed to cache messages:', error);
    }
  }

  /**
   * Get cached messages for offline access
   */
  public async getCachedMessages(): Promise<ChatMessage[]> {
    try {
      const cached = await AsyncStorage.getItem('chat_messages');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Failed to get cached messages:', error);
      return [];
    }
  }

  /**
   * Get available chats/users
   */
  public async getAvailableChats(): Promise<{ success: boolean; chats?: any[]; error?: string }> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(`${this.backendUrl}/api/chat/mobile/users`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get available chats');
      }

      return {
        success: true,
        chats: data.users || [],
      };

    } catch (error) {
      console.error('Get available chats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat users list
   */
  public async getChatUsers(): Promise<{ success: boolean; users?: any[]; currentUser?: any; error?: string }> {
    try {
      console.log('📥 Getting chat users...');

      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(
        `${this.backendUrl}/api/chat/mobile/users`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'x-device-fingerprint': await this.authService.getDeviceFingerprint(),
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get chat users');
      }

      if (data.success) {
        console.log('✅ Retrieved chat users:', data.users?.length || 0);
        return {
          success: true,
          users: data.users || [],
          currentUser: data.currentUser,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Get chat users error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get file category for display
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('application/pdf')) return 'PDF';
    if (mimeType.startsWith('application/msword') || 
        mimeType.includes('wordprocessingml')) return 'Document';
    if (mimeType.startsWith('application/vnd.ms-excel') || 
        mimeType.includes('spreadsheetml')) return 'Spreadsheet';
    if (mimeType.startsWith('text/')) return 'Text File';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.startsWith('image/')) return 'Image';
    if (mimeType.includes('zip')) return 'Archive';
    
    return 'File';
  }
}

export default ChatService;
