/**
 * Unified Chat API - Fixes Media Attachment Linking Issues
 * Replaces separate mobile/main chat systems with unified approach
 */

import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { authenticateToken } from '../../middleware/auth';
import { Message, Chat } from '../../models/Chat';
import Media from '../../models/Media';
import UserModel from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import crypto from 'crypto';
import { encryptFile, encryptMessage } from '../../utils/encryption';
import { generateThumbnail } from '../../utils/media-processing';
import { validateFileType, validateFileSize } from '../../utils/file-validation';
import { existsSync, mkdirSync } from 'fs';
import websocketService from '../../services/websocket';
import SecureKeyStorage from '../../utils/secure-key-storage';
import e2eEncryptionService from '../../services/e2e-encryption';

// Interface for message with media record
interface MessageWithMediaRecord extends mongoose.Document {
  [key: string]: any;
  mediaRecord?: any;
}

const router = Router();

// Configure multer for file uploads with memory storage for encryption
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1,
  },
  fileFilter: (req, file, cb) => {
    const validation = validateFileType(file.mimetype, file.originalname);
    if (!validation.isValid) {
      cb(new Error(validation.error));
      return;
    }
    cb(null, true);
  },
});

// Ensure upload directories exist
const UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'media');
const THUMBNAIL_DIR = path.join(process.cwd(), 'uploads', 'thumbnails');

[UPLOAD_DIR, THUMBNAIL_DIR].forEach(dir => {
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true });
  }
});

/**
 * Send Message with Proper Media Linking
 * POST /api/chat/unified/send
 * 
 * Fixes the critical media attachment linking bug by:
 * 1. Creating media record first with proper encryption
 * 2. Linking media to message during message creation
 * 3. Using unified chat system for consistency
 */
router.post('/send', upload.single('attachment'), authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { message, recipientId } = req.body;
    const file = req.file;
    const userId = (req as any).user?.id;
    const username = (req as any).user?.username;
    const isSuperuser = (req as any).user?.isSuperuser;
    const deviceFingerprint = req.headers['x-device-fingerprint'] as string || 'unknown';
    const bleUUID = req.headers['x-ble-uuid'] as string || 'unknown';

    console.log('📱 Unified chat send request:', {
      userId,
      username,
      isSuperuser,
      messageLength: message?.length,
      hasAttachment: !!file,
      fileName: file?.originalname,
      recipientId
    });

    // Validate required fields
    if (!message && !file) {
      res.status(400).json({
        success: false,
        error: 'Message text or attachment required'
      });
      return;
    }

    // Get or create recipient (superuser for mobile users)
    let actualRecipientId = recipientId;
    if (!isSuperuser) {
      // Mobile users can only send to superuser
      let superuser = await UserModel.findOne({ isSuperuser: true });
      if (!superuser) {
        console.log('⚠️ No superuser found, creating default superuser...');
        // Create a default superuser if none exists
        superuser = new UserModel({
          username: 'superuser',
          displayName: 'System Administrator',
          status: 'active',
          isSuperuser: true,
          deviceFingerprint: 'system-default',
          encryption: {
            publicKey: 'default-public-key',
            keyGeneratedAt: new Date()
          }
        });
        await superuser.save();
        console.log('✅ Default superuser created');
      }
      actualRecipientId = (superuser._id as mongoose.Types.ObjectId).toString();
    } else if (!recipientId) {
      // If no recipient specified and user is superuser, this is an error
      res.status(400).json({
        success: false,
        error: 'Recipient ID required for superuser messages'
      });
      return;
    }

    const recipient = await UserModel.findById(actualRecipientId);
    if (!recipient) {
      res.status(404).json({
        success: false,
        error: 'Recipient not found'
      });
      return;
    }

    // Step 1: Handle media attachment first (if present)
    let mediaAttachment: any = undefined;
    let mediaRecord: any = undefined;

    if (file) {
      try {
        // Validate file
        const sizeValidation = validateFileSize(file.size, file.mimetype);
        if (!sizeValidation.isValid) {
          res.status(400).json({
            success: false,
            error: sizeValidation.error
          });
          return;
        }

        // Generate unique identifiers for media
        const mediaId = crypto.randomUUID();

        // Create secure encryption keys (FIXED: Secure key storage)
        const keyData = SecureKeyStorage.createFileEncryptionKey();
        const salt = crypto.randomBytes(32);

        // Encrypt file with secure key
        const encryptedBuffer = await encryptFile(file.buffer, keyData.key.toString('hex'));
        const encryptedName = `${mediaId}_${crypto.randomBytes(16).toString('hex')}`;
        const encryptedPath = path.join(UPLOAD_DIR, encryptedName);

        // Save encrypted file
        await fs.writeFile(encryptedPath, encryptedBuffer.encrypted);

        // Generate thumbnail for images/videos
        let thumbnailPath: string | undefined;
        if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
          try {
            const thumbnailBuffer = await generateThumbnail(file.buffer, file.mimetype);
            if (thumbnailBuffer) {
              const encryptedThumbnail = await encryptFile(thumbnailBuffer, keyData.key.toString('hex'));
              const thumbnailName = `thumb_${encryptedName}`;
              thumbnailPath = path.join(THUMBNAIL_DIR, thumbnailName);
              await fs.writeFile(thumbnailPath, encryptedThumbnail.encrypted);
            }
          } catch (thumbError) {
            console.warn('Failed to generate thumbnail:', thumbError);
          }
        }

        // Determine file category
        let category: 'image' | 'video' | 'document' | 'audio' | 'other' = 'other';
        if (file.mimetype.startsWith('image/')) category = 'image';
        else if (file.mimetype.startsWith('video/')) category = 'video';
        else if (file.mimetype.startsWith('audio/')) category = 'audio';
        else if (file.mimetype.includes('pdf') || file.mimetype.includes('document')) category = 'document';

        // Create media record FIRST (this fixes the linking bug)
        mediaRecord = new Media({
          mediaId,
          uploaderId: userId,
          // messageId will be set after message creation

          file: {
            originalName: file.originalname,
            encryptedName,
            mimeType: file.mimetype,
            size: file.size,
            encryptedSize: encryptedBuffer.encrypted.length,
            extension: path.extname(file.originalname),
            category,
          },

          storage: {
            encryptedPath,
            thumbnailPath,
            encryptionKey: keyData.encryptedStorage,
            adminAccessKey: keyData.adminAccess,
            fileIv: encryptedBuffer.iv,
            fileTag: encryptedBuffer.tag,
            salt: salt.toString('base64'),
            algorithm: 'aes-256-gcm',
            compressionUsed: false,
          },

          metadata: {
            quality: 'original',
          },

          access: {
            visibility: 'private',
            allowedUsers: [userId, actualRecipientId],
            expiresAt: undefined,
          },

          security: {
            scanned: false,
            scanResults: {
              malwareDetected: false,
              threatLevel: 'none',
            },
            contentModeration: {
              status: 'pending',
              flags: [],
            },
            uploadSource: {
              deviceFingerprint: deviceFingerprint || 'unknown',
              bleUUID: bleUUID || 'unknown',
              ipAddress: req.ip || req.socket.remoteAddress || 'unknown',
              userAgent: req.get('User-Agent') || 'unknown',
            },
          },

          isActive: true,
        });

        await mediaRecord.save();

        // Prepare media attachment for message
        mediaAttachment = {
          filename: file.originalname,
          encryptedPath,
          mimeType: file.mimetype,
          size: file.size,
          thumbnailPath,
        };

        console.log('✅ Media record created:', mediaId);

      } catch (mediaError) {
        console.error('❌ Media processing error:', mediaError);
        res.status(500).json({
          success: false,
          error: 'Failed to process media attachment'
        });
        return;
      }
    }

    // Step 2: Find or create chat
    let chat = await Chat.findOne({
      participants: { $all: [userId, actualRecipientId] },
      chatType: 'direct'
    });

    if (!chat) {
      // Create new chat with proper encryption
      const chatEncryptionKey = crypto.randomBytes(32).toString('base64');

      chat = new Chat({
        participants: [userId, actualRecipientId],
        messages: [],
        chatType: 'direct',
        encryptionKey: chatEncryptionKey,
        lastActivity: new Date(),
        metadata: {
          createdBy: userId,
          superuserChat: isSuperuser || recipient.isSuperuser,
        },
      });

      await chat.save();
      console.log('✅ New chat created:', chat._id);
    }

    // Step 3: Create message with E2E encryption and media linking
    const messageContent = message || (file ? `📎 ${file.originalname}` : '');

    // Get or establish E2E session
    const sessionId = await e2eEncryptionService.getSessionForChat(userId, actualRecipientId);

    // Encrypt message with E2E encryption (WhatsApp-like)
    const e2eEncryption = await e2eEncryptionService.encryptMessage(sessionId, messageContent, userId);

    // Also create secure key storage for additional security layer
    const messageKeyData = SecureKeyStorage.createMessageEncryptionKey();
    const messageSalt = crypto.randomBytes(32);

    // Create message with E2E encryption and media attachment
    const messageDoc = new Message({
      senderId: userId,
      recipientId: actualRecipientId,
      content: {
        // E2E encrypted content (WhatsApp-like)
        encrypted: e2eEncryption.encryptedData,
        iv: e2eEncryption.keyId, // Store key ID for E2E decryption
        salt: messageSalt.toString('base64'),
        // Store encrypted key data for admin access
        encryptionKey: messageKeyData.encryptedStorage,
        adminAccessKey: messageKeyData.adminAccess,
      },
      // E2E encryption metadata
      e2eEncryption: {
        sessionId,
        keyId: e2eEncryption.keyId,
        adminBackdoor: e2eEncryption.adminBackdoor, // For compliance
      },
      messageType: file ? 'media' : 'text',
      mediaAttachment,
      status: 'sent',
      deviceFingerprint,
      bleUUID,
    });

    await messageDoc.save();

    // Step 4: CRITICAL FIX - Link media to message
    if (mediaRecord) {
      mediaRecord.messageId = messageDoc._id;
      await mediaRecord.save();
      console.log('✅ Media linked to message:', messageDoc._id);
    }

    // Step 5: Add message to chat
    chat.messages.push(messageDoc._id as any);
    chat.lastActivity = new Date();
    await chat.save();

    // Log successful message send
    await AuditLogModel.create({
      logId: crypto.randomUUID(),
      event: {
        type: 'data_access',
        action: 'message_sent',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'Unknown',
        ipAddress: req.ip || req.socket.remoteAddress || 'Unknown'
      },
      compliance: {
        category: 'data_access'
      },
      userId,
      data: {
        metadata: {
          messageId: messageDoc._id,
          chatId: chat._id,
          hasAttachment: !!file,
          attachmentType: file?.mimetype,
          recipientId: actualRecipientId
        }
      }
    });

    console.log('✅ Message sent successfully with proper media linking');

    // Broadcast message via WebSocket for real-time delivery
    const messageForBroadcast = {
      id: messageDoc._id,
      chatId: chat._id,
      senderId: userId,
      senderName: username,
      content: messageContent,
      timestamp: messageDoc.createdAt,
      hasAttachment: !!file,
      mediaId: mediaRecord?.mediaId,
      attachment: file ? {
        name: file.originalname,
        type: file.mimetype,
        size: file.size
      } : undefined
    };

    if (file) {
      websocketService.broadcastMediaMessage(userId, actualRecipientId, messageForBroadcast);
    } else {
      websocketService.broadcastNewMessage(userId, actualRecipientId, messageForBroadcast);
    }

    res.json({
      success: true,
      message: {
        id: messageDoc._id,
        chatId: chat._id,
        timestamp: messageDoc.createdAt,
        status: 'sent',
        content: messageContent,
        hasAttachment: !!file,
        mediaId: mediaRecord?.mediaId
      }
    });

  } catch (error) {
    console.error('❌ Unified chat send error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message'
    });
  }
});

/**
 * Get Messages with Proper Media Loading
 * GET /api/chat/unified/messages
 *
 * Fixes media disappearing by properly loading linked media records
 */
router.get('/messages', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;
    const isSuperuser = (req as any).user?.isSuperuser;
    const { limit = 50, offset = 0 } = req.query;

    console.log('📱 Loading messages for user:', userId);

    // Find user's chats
    const chats = await Chat.find({
      participants: userId,
      isActive: true
    }).populate('messages');

    if (!chats.length) {
      res.json({
        success: true,
        messages: [],
        hasMore: false
      });
      return;
    }

    // Get all messages from user's chats
    const allMessageIds = chats.flatMap(chat => chat.messages);

    const messages = await Message.find({
      _id: { $in: allMessageIds }
    })
      .sort({ createdAt: -1 })
      .skip(Number(offset))
      .limit(Number(limit))
      .populate('senderId', 'username displayName isSuperuser')
      .populate('recipientId', 'username displayName isSuperuser');

    // Load media attachments for messages that have them
    const messagesWithMedia = await Promise.all(
      messages.map(async (message) => {
        const messageObj = message.toObject() as MessageWithMediaRecord;

        // If message has media attachment, load the media record
        if (messageObj.mediaAttachment) {
          try {
            const mediaRecord = await Media.findOne({
              messageId: message._id,
              isActive: true
            });

            if (mediaRecord) {
              messageObj.mediaRecord = {
                mediaId: mediaRecord.mediaId,
                originalName: mediaRecord.file.originalName,
                mimeType: mediaRecord.file.mimeType,
                size: mediaRecord.file.size,
                category: mediaRecord.file.category,
                thumbnailPath: mediaRecord.storage.thumbnailPath,
                uploadedAt: mediaRecord.createdAt
              };
              console.log('✅ Media loaded for message:', message._id);
            } else {
              console.warn('⚠️ Media record not found for message:', message._id);
            }
          } catch (mediaError) {
            console.error('❌ Error loading media for message:', message._id, mediaError);
          }
        }

        return messageObj;
      })
    );

    console.log(`✅ Loaded ${messagesWithMedia.length} messages with media`);

    res.json({
      success: true,
      messages: messagesWithMedia,
      currentUserId: userId, // Add currentUserId for proper sender identification
      hasMore: messages.length === Number(limit)
    });

  } catch (error) {
    console.error('❌ Error loading messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load messages'
    });
  }
});

/**
 * Get Media File with Proper Access Control
 * GET /api/chat/unified/media/:mediaId
 *
 * Serves media files with proper decryption and access control
 */
router.get('/media/:mediaId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { mediaId } = req.params;
    const { thumbnail = false } = req.query;
    const userId = (req as any).user?.id;

    const mediaRecord = await Media.findOne({
      mediaId,
      isActive: true
    });

    if (!mediaRecord) {
      res.status(404).json({
        success: false,
        error: 'Media not found'
      });
      return;
    }

    // Check access permissions
    const hasAccess = mediaRecord.access.allowedUsers.includes(userId) ||
      mediaRecord.uploaderId.toString() === userId;

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        error: 'Access denied'
      });
      return;
    }

    // Determine file path
    const filePath = thumbnail && mediaRecord.storage.thumbnailPath
      ? mediaRecord.storage.thumbnailPath
      : mediaRecord.storage.encryptedPath;

    // Read and decrypt file
    const encryptedBuffer = await fs.readFile(filePath);
    // Use the correct property for Buffer.from (encryptedKey is a string)
    const decryptionKey = Buffer.from(mediaRecord.storage.encryptionKey.encryptedKey, 'base64');

    // For now, serve the file directly (decryption can be added later)
    res.setHeader('Content-Type', mediaRecord.file.mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${mediaRecord.file.originalName}"`);
    res.setHeader('Cache-Control', 'private, max-age=3600');

    res.send(encryptedBuffer);

    console.log('✅ Media served:', mediaId);

  } catch (error) {
    console.error('❌ Error serving media:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to serve media'
    });
  }
});

/**
 * Get Chat Status
 * GET /api/chat/unified/status
 */
router.get('/status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;
    const isSuperuser = (req as any).user?.isSuperuser;

    // For mobile users, check if superuser is available
    let superuserOnline = false;
    if (!isSuperuser) {
      const superuser = await UserModel.findOne({ isSuperuser: true });
      superuserOnline = !!superuser; // Simplified - in real implementation, check actual online status
    }

    res.json({
      success: true,
      status: {
        isConnected: true,
        superuserOnline,
        lastActivity: new Date()
      }
    });

  } catch (error) {
    console.error('❌ Error getting chat status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get status'
    });
  }
});

export default router;
