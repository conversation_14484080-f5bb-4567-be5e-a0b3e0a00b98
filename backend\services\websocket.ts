/**
 * WebSocket Service for Real-time Messaging
 * Replaces polling with instant message delivery
 * No typing indicators, online status, or read receipts as per requirements
 */

import { Server } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import jwt from 'jsonwebtoken';
import UserModel from '../models/User';
import { realTimeVoiceStreamingService } from './realTimeVoiceStreaming';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: string;
  username?: string;
  isSuperuser?: boolean;
  isAlive?: boolean;
}

interface WebSocketMessage {
  type: 'message' | 'media' | 'system' | 'error' | 'admin_monitor' | 'voice_call' | 'audio_frame' | 'call_signal';
  data: any;
  timestamp: number;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, AuthenticatedWebSocket> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize WebSocket server
   */
  public initialize(server: Server): void {
    this.wss = new WebSocketServer({
      server,
      path: '/ws/chat',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();

    console.log('✅ WebSocket service initialized for real-time messaging');
  }

  /**
   * Verify client authentication
   */
  private async verifyClient(info: any): Promise<boolean> {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('❌ WebSocket connection rejected: No token provided');
        return false;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;

      // Attach user info to request for later use
      info.req.userId = decoded.userId || decoded.adminId;
      info.req.username = decoded.username;
      info.req.isSuperuser = decoded.isSuperuser || false;

      return true;
    } catch (error) {
      console.log('❌ WebSocket authentication failed:', error);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: any): void {
    const userId = req.userId;
    const username = req.username;
    const isSuperuser = req.isSuperuser;

    // Set up authenticated WebSocket
    ws.userId = userId;
    ws.username = username;
    ws.isSuperuser = isSuperuser;
    ws.isAlive = true;

    // Store client connection
    this.clients.set(userId, ws);

    console.log(`✅ WebSocket connected: ${username} (${userId})`);

    // Set up message handling
    ws.on('message', (data) => {
      let buffer: Buffer;
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (typeof data === 'string') {
        buffer = Buffer.from(data);
      } else if (data instanceof ArrayBuffer) {
        buffer = Buffer.from(new Uint8Array(data));
      } else {
        buffer = Buffer.alloc(0);
      }
      this.handleMessage(ws, buffer);
    }); ws.on('close', () => this.handleDisconnection(ws));
    ws.on('error', (error) => this.handleError(ws, error));
    ws.on('pong', () => { ws.isAlive = true; });

    // No automatic welcome message as per requirements
  }

  /**
   * Handle incoming WebSocket messages
   */
  private async handleMessage(ws: AuthenticatedWebSocket, data: Buffer): Promise<void> {
    try {
      const message = JSON.parse(data.toString());

      switch (message.type) {
        case 'ping':
          this.sendToClient(ws, {
            type: 'system',
            data: { message: 'pong' },
            timestamp: Date.now()
          });
          break;

        case 'voice_call_connect':
          await this.handleVoiceCallConnect(ws, message.data);
          break;

        case 'audio_frame':
          await this.handleAudioFrame(ws, message.data);
          break;

        case 'call_signal':
          await this.handleCallSignal(ws, message.data);
          break;

        default:
          // For other message types, still go through HTTP API for proper encryption/storage
          break;
      }
    } catch (error) {
      console.error('❌ Error handling WebSocket message:', error);
      this.sendToClient(ws, {
        type: 'error',
        data: { message: 'Invalid message format' },
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle voice call WebRTC connection
   */
  private async handleVoiceCallConnect(ws: AuthenticatedWebSocket, data: any): Promise<void> {
    try {
      const { callId } = data;
      if (!ws.userId || !callId) {
        throw new Error('Missing userId or callId');
      }

      const success = await realTimeVoiceStreamingService.connectWebRTCSession(
        callId,
        ws.userId,
        ws
      );

      this.sendToClient(ws, {
        type: 'voice_call',
        data: {
          action: 'connect_response',
          success,
          callId
        },
        timestamp: Date.now()
      });

      console.log(`🎤 Voice call WebRTC connected: ${ws.username} to call ${callId}`);
    } catch (error) {
      console.error('Voice call connection error:', error);
      this.sendToClient(ws, {
        type: 'error',
        data: { message: 'Failed to connect to voice call' },
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle incoming audio frame for voice morphing
   */
  private async handleAudioFrame(ws: AuthenticatedWebSocket, data: any): Promise<void> {
    try {
      const { sessionId, audioData } = data;
      if (!sessionId || !audioData) {
        throw new Error('Missing sessionId or audioData');
      }

      // Convert audio data back to Float32Array
      const audioFloat32 = new Float32Array(audioData);

      // Process and forward the audio frame
      const morphedAudio = await realTimeVoiceStreamingService.processAudioFrame(
        sessionId,
        audioFloat32
      );

      if (morphedAudio) {
        await realTimeVoiceStreamingService.forwardAudioFrame(sessionId, morphedAudio);
      }
    } catch (error) {
      console.error('Audio frame processing error:', error);
      this.sendToClient(ws, {
        type: 'error',
        data: { message: 'Audio processing failed' },
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle call signaling messages
   */
  private async handleCallSignal(ws: AuthenticatedWebSocket, data: any): Promise<void> {
    try {
      const { callId, signal, targetUserId } = data;

      // Forward signaling message to target user
      const targetWs = this.clients.get(targetUserId);
      if (targetWs) {
        this.sendToClient(targetWs, {
          type: 'call_signal',
          data: {
            callId,
            signal,
            fromUserId: ws.userId
          },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Call signaling error:', error);
    }
  }

  /**
   * Handle WebSocket disconnection
   */
  private handleDisconnection(ws: AuthenticatedWebSocket): void {
    if (ws.userId) {
      this.clients.delete(ws.userId);
      console.log(`❌ WebSocket disconnected: ${ws.username} (${ws.userId})`);
    }
  }

  /**
   * Handle WebSocket errors
   */
  private handleError(ws: AuthenticatedWebSocket, error: Error): void {
    console.error(`❌ WebSocket error for ${ws.username}:`, error);
  }

  /**
   * Send message to specific client
   */
  private sendToClient(ws: AuthenticatedWebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast new message to relevant users
   * Called from HTTP API after message is saved to database
   */
  public broadcastNewMessage(senderId: string, recipientId: string, messageData: any): void {
    const message: WebSocketMessage = {
      type: 'message',
      data: messageData,
      timestamp: Date.now()
    };

    // Send to recipient
    const recipientWs = this.clients.get(recipientId);
    if (recipientWs) {
      this.sendToClient(recipientWs, message);
      console.log(`📤 Real-time message sent to ${recipientWs.username}`);
    }

    // Send confirmation to sender (if different from recipient)
    if (senderId !== recipientId) {
      const senderWs = this.clients.get(senderId);
      if (senderWs) {
        this.sendToClient(senderWs, {
          ...message,
          data: { ...messageData, isOwnMessage: true }
        });
      }
    }

    // Send to all connected admin users for real-time monitoring
    this.broadcastToAdmins(message);
  }

  /**
   * Broadcast media attachment notification
   */
  public broadcastMediaMessage(senderId: string, recipientId: string, mediaData: any): void {
    const message: WebSocketMessage = {
      type: 'media',
      data: mediaData,
      timestamp: Date.now()
    };

    // Send to recipient
    const recipientWs = this.clients.get(recipientId);
    if (recipientWs) {
      this.sendToClient(recipientWs, message);
      console.log(`📤 Real-time media message sent to ${recipientWs.username}`);
    }

    // Send confirmation to sender
    if (senderId !== recipientId) {
      const senderWs = this.clients.get(senderId);
      if (senderWs) {
        this.sendToClient(senderWs, {
          ...message,
          data: { ...mediaData, isOwnMessage: true }
        });
      }
    }

    // Send to all connected admin users for real-time monitoring
    this.broadcastToAdmins(message);
  }

  /**
   * Broadcast message to all connected admin users for real-time monitoring
   */
  private broadcastToAdmins(message: WebSocketMessage): void {
    this.clients.forEach((ws) => {
      if (ws.isSuperuser) {
        this.sendToClient(ws, {
          ...message,
          type: 'admin_monitor',
          data: { ...message.data, isAdminMonitor: true }
        });
      }
    });
  }

  /**
   * Get connected users count
   */
  public getConnectedUsersCount(): number {
    return this.clients.size;
  }

  /**
   * Check if user is connected
   */
  public isUserConnected(userId: string): boolean {
    return this.clients.has(userId);
  }

  /**
   * Broadcast voice call event to participants
   */
  public broadcastVoiceCallEvent(callId: string, participantIds: string[], eventData: any): void {
    const message: WebSocketMessage = {
      type: 'voice_call',
      data: {
        callId,
        ...eventData
      },
      timestamp: Date.now()
    };

    participantIds.forEach(userId => {
      const ws = this.clients.get(userId);
      if (ws) {
        this.sendToClient(ws, message);
      }
    });
  }

  /**
   * Send voice call invitation
   */
  public sendVoiceCallInvitation(callId: string, fromUserId: string, toUserId: string, profileData: any): void {
    const targetWs = this.clients.get(toUserId);
    if (targetWs) {
      this.sendToClient(targetWs, {
        type: 'voice_call',
        data: {
          action: 'incoming_call',
          callId,
          fromUserId,
          profileData
        },
        timestamp: Date.now()
      });
    }
  }

  /**
   * Start heartbeat to detect dead connections
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach((ws, userId) => {
        if (!ws.isAlive) {
          console.log(`💀 Removing dead WebSocket connection: ${userId}`);
          ws.terminate();
          this.clients.delete(userId);
          return;
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // Check every 30 seconds
  }

  /**
   * Cleanup WebSocket service
   */
  public cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.clients.forEach((ws) => {
      ws.close();
    });

    this.clients.clear();

    if (this.wss) {
      this.wss.close();
    }

    console.log('🧹 WebSocket service cleaned up');
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
