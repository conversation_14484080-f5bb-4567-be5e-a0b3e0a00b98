/**
 * WORLD Vocoder Service for Real-time Voice Transformation
 * Provides high-level interface to WORLD vocoder native addon
 * Designed for <100ms latency voice calls with non-reversible morphing
 */

import * as crypto from 'crypto';
import { EventEmitter } from 'events';
import { voiceSecurityService } from './voiceSecurityService';
import * as path from 'path';

// Import the native addon (will be compiled)
let worldVocoder: any = null;
const addonPaths = [
  path.join(__dirname, '../build/Release/world_vocoder.node'),
  path.join(__dirname, '../../backend/build/Release/world_vocoder.node'),
  path.join(process.cwd(), 'backend/build/Release/world_vocoder.node'),
  path.join(process.cwd(), 'build/Release/world_vocoder.node')
];

for (const addonPath of addonPaths) {
  try {
    worldVocoder = require(addonPath);
    console.log('✅ WORLD vocoder native addon loaded successfully from:', addonPath);
    console.log('✅ Available methods:', Object.keys(worldVocoder));
    break;
  } catch (error) {
    console.log('⚠️ Failed to load from:', addonPath, 'Error:', error instanceof Error ? error.message : String(error));
    // Continue trying other paths
  }
}

if (!worldVocoder) {
  console.error('❌ WORLD vocoder native addon not available in any of the following paths:');
  addonPaths.forEach(p => console.error('   -', p));
  console.error('❌ CRITICAL: Native WORLD vocoder is required but not available');
  console.error('❌ Please build the native addon with: npm run build:world');
}

export interface WorldVoiceProfile {
  pitchScale: number;      // 0.7-1.3 (pitch modification)
  spectralWarp: number;    // -10% to +10% (formant shifting)
  reverbAmount: number;    // 0-50% (spatial distortion)
  eqTilt: number;         // -6dB to +6dB (frequency emphasis)
  temporalJitter: number; // Anti-forensic timing variation
  spectralNoise: number;  // Irreversible spectral masking
  antiForensic: boolean;  // Enable anti-forensic processing
}

export interface WorldProcessingConfig {
  sampleRate: number;     // Audio sample rate (typically 48000)
  frameSize: number;      // Frame size in samples (typically 960 for 20ms)
  realTimeMode: boolean;  // Enable real-time optimizations
  qualityLevel: 'fast' | 'balanced' | 'high';
}

export interface WorldFeatures {
  f0: Float64Array;                    // Fundamental frequency
  spectralEnvelope: Float64Array;      // Spectral envelope (flattened 2D)
  aperiodicity: Float64Array;          // Aperiodicity (flattened 2D)
  spectralFrames: number;              // Number of spectral frames
  spectralBins: number;                // Number of spectral bins
}

export interface ProcessingStats {
  latency: number;        // Processing latency in ms
  frameCount: number;     // Total frames processed
  errorCount: number;     // Number of processing errors
  averageLatency: number; // Average latency over session
}

/**
 * WORLD Vocoder Processing Session
 * Manages a single voice processing session with state
 */
class WorldVocoderSession extends EventEmitter {
  private sessionId: string;
  private processor: any = null;
  private config: WorldProcessingConfig;
  private stats: ProcessingStats;
  private isActive: boolean = false;
  private securitySessionId?: string;
  private processingDepth: number = 0; // Add recursion guard

  constructor(sessionId: string, config: WorldProcessingConfig) {
    super();
    this.sessionId = sessionId;
    this.config = config;
    this.stats = {
      latency: 0,
      frameCount: 0,
      errorCount: 0,
      averageLatency: 0
    };
  }

  /**
   * Initialize the processing session
   */
  async initialize(): Promise<boolean> {
    try {
      // Require native WORLD vocoder - no fallback allowed
      if (!worldVocoder) {
        throw new Error('Native WORLD vocoder not available - initialization failed');
      }
      
      this.processor = worldVocoder.initializeProcessor({
        sampleRate: this.config.sampleRate,
        frameSize: this.config.frameSize,
        realTimeMode: this.config.realTimeMode
      });
      console.log('✅ Native WORLD vocoder initialized');

      // Initialize security session ONLY for anti-forensic profiles
      // Skip security for NORMAL_VOICE to prevent audio corruption
      try {
        this.securitySessionId = await voiceSecurityService.createSecuritySession(
          this.sessionId, // Using sessionId as userId for now
          {
            antiForensicLevel: 'high',
            temporalJitterRange: 0.05,
            spectralNoiseLevel: 0.15,
            glottalDestruction: true,
            formantScrambling: true,
            cryptographicSigning: true,
            originalWaveformPurge: true
          }
        );
        console.log('🔒 Security session created: {sessionId} for user: {userId}', {sessionId: this.securitySessionId, userId: this.sessionId});
        console.log('🔒 Voice security session initialized');
      } catch (securityError) {
        console.warn('⚠️ Security session initialization failed, continuing without security features:', securityError);
        this.securitySessionId = undefined;
      }

      this.isActive = true;
      this.emit('initialized', this.sessionId);
      return true;
    } catch (error) {
      console.error('Failed to initialize voice processor:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Process audio frame with voice morphing
   */
  async processFrame(audioData: Float32Array, profile: WorldVoiceProfile): Promise<Float32Array | null> {
    if (!this.isActive) {
      console.warn('Session is not active, cannot process frame');
      return null;
    }

    // CRITICAL: Sanitize profile parameters to prevent audio clipping
    const sanitizedProfile = sanitizeVoiceProfile(profile);
    
    console.log('🔧 Profile sanitization:');
    console.log('   Original profile:', JSON.stringify(profile));
    console.log('   Sanitized profile:', JSON.stringify(sanitizedProfile));

    // Recursion guard
    this.processingDepth++;
    if (this.processingDepth > 10) {
      console.error('❌ RECURSION DETECTED: Processing depth exceeded 10, aborting to prevent stack overflow');
      this.processingDepth--;
      throw new Error('Maximum processing recursion depth exceeded');
    }

    console.log(`🔧 Processing frame (depth: ${this.processingDepth}) with session:`, this.sessionId);

    try {
      console.log('🔧 Processing frame with session:', this.sessionId);
      const startTime = Date.now();

      let result: Float32Array;

      // Skip native processing entirely for NORMAL_VOICE profile 
      // to ensure fastest possible performance for identity profile
      const isNormalProfile = 
        Math.abs(sanitizedProfile.pitchScale - 1.0) < 0.01 &&
        Math.abs(sanitizedProfile.spectralWarp) < 0.01 &&
        sanitizedProfile.reverbAmount < 0.01 &&
        Math.abs(sanitizedProfile.eqTilt) < 0.01 &&
        sanitizedProfile.temporalJitter < 0.01 &&
        sanitizedProfile.spectralNoise < 0.01 &&
        !sanitizedProfile.antiForensic;

      if (isNormalProfile) {
        console.log('🔧 NORMAL_VOICE profile detected - returning original audio without processing');
        result = new Float32Array(audioData);
      } else if (sanitizedProfile.antiForensic && this.shouldUseJsProcessing(sanitizedProfile)) {
        // Use JS processing for problematic anti-forensic profiles
        console.log('🔒 Anti-forensic profile detected - using JavaScript processing for better quality');

        // Import the JS Voice Processor
        const { jsVoiceProcessor } = require('./jsVoiceProcessor');
        
        // Use JS processor for anti-forensic profiles
        result = jsVoiceProcessor.processAudio(audioData, sanitizedProfile);
        console.log('✅ JS Voice Processor completed for anti-forensic profile, result length:', result?.length);
      } else if (this.shouldUseJsProcessing(sanitizedProfile)) {
        // For profiles with extreme transformations, use JS processing as well
        console.log('⚠️ Profile has extreme parameters that may cause issues with native processor - using JS fallback');
        
        const { jsVoiceProcessor } = require('./jsVoiceProcessor');
        result = jsVoiceProcessor.processAudio(audioData, sanitizedProfile);
        console.log('✅ JS Voice Processor completed for extreme profile, result length:', result?.length);
      } else if (worldVocoder && this.processor) {
        console.log('🔧 Using native WORLD vocoder for processing');
        
        // Safe way to get min/max for large arrays
        let audioMin = Infinity;
        let audioMax = -Infinity;
        for (let i = 0; i < Math.min(audioData.length, 1000); i += 100) { // Sample every 100th element, max 1000 elements
          audioMin = Math.min(audioMin, audioData[i]);
          audioMax = Math.max(audioMax, audioData[i]);
        }
        console.log('🔧 Audio data validation - length:', audioData.length, 'sample min:', audioMin, 'sample max:', audioMax);
        
        try {
          console.log('🔧 About to call native processAudioFrame...');
          
          // Validate audio data before processing
          if (audioData.length === 0) {
            throw new Error('Audio data is empty');
          }
          
          if (audioData.length > 1000000) { // 1M samples ~20 seconds at 48kHz
            throw new Error(`Audio data too large: ${audioData.length} samples`);
          }
          
          // Check for invalid audio data (sample only first 1000 elements for performance)
          for (let i = 0; i < Math.min(audioData.length, 1000); i += 10) {
            if (isNaN(audioData[i]) || !isFinite(audioData[i])) {
              throw new Error(`Audio data contains NaN or infinite values at index ${i}`);
            }
          }
          
          console.log('🔧 Audio data validation passed, calling native WORLD vocoder...');
          console.log('🔧 Processor object:', this.processor ? 'exists' : 'null');
          console.log('🔧 Profile (sanitized):', JSON.stringify(sanitizedProfile));
          
          // Use native WORLD vocoder with SANITIZED profile to prevent clipping
          result = worldVocoder.processAudioFrame(
            this.processor,
            audioData,
            sanitizedProfile  // Use sanitized profile instead of original
          );
          
          console.log('✅ Native WORLD vocoder processing completed, result length:', result?.length);
          
          // CRITICAL: Validate output size - WORLD vocoder should maintain input/output size ratio
          if (result && result.length !== audioData.length) {
            console.error('❌ CRITICAL: Native vocoder returned wrong output size!');
            console.error('❌ Input size:', audioData.length, 'Output size:', result.length);
            console.error('❌ Expected 1:1 ratio, got ratio:', (result.length / audioData.length).toFixed(2));
            
            if (result.length > audioData.length * 1.1) {
              console.error('❌ Output is too large - this indicates a bug in the native vocoder');
              console.error('❌ Truncating to input size to prevent audio corruption');
              const correctSizeResult = new Float32Array(audioData.length);
              correctSizeResult.set(result.subarray(0, audioData.length));
              result = correctSizeResult;
              console.log('✅ Output corrected to:', result.length, 'samples');
            } else if (result.length < audioData.length * 0.9) {
              console.error('❌ Output is too small - padding with silence');
              const paddedResult = new Float32Array(audioData.length);
              paddedResult.set(result.subarray(0, Math.min(result.length, audioData.length)));
              result = paddedResult;
              console.log('✅ Output padded to:', result.length, 'samples');
            }
          }
          
          // CRITICAL: Check for NaN values in output - this is a common issue with non-normal profiles
          let hasNaN = false;
          if (result) {
            for (let i = 0; i < Math.min(result.length, 10000); i += 100) {
              if (isNaN(result[i]) || !isFinite(result[i])) {
                hasNaN = true;
                console.error(`❌ CRITICAL: Native vocoder returned NaN/Infinity at index ${i}`);
                break;
              }
            }
          }
          
          // If we have NaN values, force fallback to JS processor
          if (hasNaN) {
            throw new Error('Native vocoder returned NaN values - falling back to JS processor');
          }
          
          // Validate audio quality - check for excessive noise or silence
          if (result && result.length > 0) {
            let audioSum = 0;
            let maxAmplitude = 0;
            let noiseCount = 0;
            let saturatedCount = 0; // Count samples at exactly 1.0 or -1.0

            for (let i = 0; i < Math.min(result.length, 10000); i += 10) {
              const abs = Math.abs(result[i]);
              audioSum += abs;
              maxAmplitude = Math.max(maxAmplitude, abs);

              // Check for saturation (samples at exactly 1.0 or -1.0)
              if (abs >= 0.999) {
                saturatedCount++;
              }

              // Check for potential noise (very high frequency changes)
              if (i > 10 && Math.abs(result[i] - result[i-10]) > 0.5) {
                noiseCount++;
              }
            }
            const averageAmplitude = audioSum / (Math.min(result.length, 10000) / 10);
            const noiseRatio = noiseCount / (Math.min(result.length, 10000) / 10);
            const saturationRatio = saturatedCount / (Math.min(result.length, 10000) / 10);

            console.log('🔧 Output audio quality check:');
            console.log('   - Average amplitude:', averageAmplitude.toFixed(4));
            console.log('   - Max amplitude:', maxAmplitude.toFixed(4));
            console.log('   - Noise ratio:', (noiseRatio * 100).toFixed(2) + '%');
            console.log('   - Saturation ratio:', (saturationRatio * 100).toFixed(2) + '%');

            // CRITICAL: Check for saturation (all samples at 1.0) - this is the main issue
            if (saturationRatio > 0.8) {
              console.error('❌ CRITICAL: Native vocoder returned saturated output (' + (saturationRatio * 100).toFixed(1) + '% saturated)');
              console.error('❌ This indicates a bug in the native vocoder - falling back to JS processor');
              throw new Error('Native vocoder returned saturated output - falling back to JS processor');
            }

            // If audio seems corrupted, log detailed warning
            if (averageAmplitude < 0.001) {
              console.warn('⚠️ Output audio is very quiet (avg < 0.001) - might be corrupted or silent');

              // For extremely quiet output, fall back to JS processor
              if (averageAmplitude < 0.0001 && !isNormalProfile) {
                throw new Error('Native vocoder returned extremely quiet output - falling back to JS processor');
              }
            } else if (averageAmplitude > 0.8) {
              console.warn('⚠️ Output audio is very loud (avg > 0.8) - might be corrupted or clipping');
            }

            if (noiseRatio > 0.5) {
              console.warn('⚠️ High noise ratio detected (' + (noiseRatio * 100).toFixed(1) + '%) - output may be corrupted');

              // For extremely noisy output, fall back to JS processor
              if (noiseRatio > 0.7 && !isNormalProfile) {
                throw new Error('Native vocoder returned extremely noisy output - falling back to JS processor');
              }
            }
          }
        } catch (nativeError) {
          console.error('❌ Native WORLD vocoder failed:', nativeError);
          console.error('❌ Native error details:', {
            message: nativeError instanceof Error ? nativeError.message : 'Unknown error',
            stack: nativeError instanceof Error ? nativeError.stack : 'No stack trace',
            audioLength: audioData.length,
            profile: profile
          });
          
          // Import the JS Voice Processor here to prevent circular dependencies
          const { jsVoiceProcessor } = require('./jsVoiceProcessor');
          
          // FALLBACK TO JS PROCESSOR
          console.log('🔄 Native WORLD vocoder failed - FALLING BACK to JavaScript processor');
          result = jsVoiceProcessor.processAudio(audioData, sanitizedProfile);
          console.log('✅ JS Voice Processor fallback completed, result length:', result?.length);
        }
      } else {
        // Native processor not available, use JavaScript fallback
        console.log('⚠️ Native WORLD vocoder not available - using JavaScript fallback');
        
        // Import the JS Voice Processor
        const { jsVoiceProcessor } = require('./jsVoiceProcessor');
        
        // Use JS processor
        result = jsVoiceProcessor.processAudio(audioData, sanitizedProfile);
        console.log('✅ JS Voice Processor completed, result length:', result?.length);
      }

      // TEMPORARILY DISABLE security transformations to isolate audio quality issues
      // Security processing is disabled for debugging purposes
      console.log('🔓 Skipping security transformations (TEMPORARILY DISABLED FOR AUDIO QUALITY DEBUGGING)');
      
      const totalProcessingTime = Date.now() - startTime;
      this.updateStats(totalProcessingTime);

      this.emit('frameProcessed', {
        sessionId: this.sessionId,
        latency: totalProcessingTime,
        inputSize: audioData.length,
        outputSize: result.length
      });

      console.log(`✅ Frame processed in ${totalProcessingTime}ms for profile ${JSON.stringify({
        pitchScale: sanitizedProfile.pitchScale,
        spectralWarp: sanitizedProfile.spectralWarp,
        antiForensic: sanitizedProfile.antiForensic
      })}`);

      // Release recursion guard
      this.processingDepth--;
      return result;
    } catch (error) {
      console.error('❌ Critical error in processFrame:', error);
      if (error instanceof Error) {
        console.error('❌ Error stack:', error.stack);
      }
      console.error('❌ Error at frame processing stage');
      
      // Release recursion guard on error
      this.processingDepth--;
      
      // Update error stats
      this.stats.errorCount++;
      this.emit('processingError', error);
      
      // Last resort - return original audio when all else fails
      console.log('⚠️ All processing options failed - returning original audio as fallback');
      return new Float32Array(audioData);
    }
  }

  /**
   * Extract WORLD features from audio
   */
  extractFeatures(audioData: Float32Array): WorldFeatures | null {
    if (!this.isActive || !this.processor) {
      return null;
    }

    try {
      return worldVocoder.extractWorldFeatures(this.processor, audioData);
    } catch (error) {
      this.emit('error', error);
      return null;
    }
  }

  /**
   * Synthesize audio from WORLD features
   */
  synthesizeAudio(features: WorldFeatures): Float32Array | null {
    if (!this.isActive || !this.processor) {
      return null;
    }

    try {
      return worldVocoder.synthesizeAudio(this.processor, features);
    } catch (error) {
      this.emit('error', error);
      return null;
    }
  }

  /**
   * Get processing statistics
   */
  getStats(): ProcessingStats {
    return { ...this.stats };
  }

  /**
   * Get security metrics for this session
   */
  getSecurityMetrics() {
    if (!this.securitySessionId) {
      return null;
    }
    return voiceSecurityService.getSecurityMetrics(this.securitySessionId);
  }

  /**
   * Verify audio security
   */
  async verifyAudioSecurity(audioData: Float32Array, metadata: any) {
    if (!this.securitySessionId) {
      return {
        isSecure: false,
        reversibilityRisk: 1.0,
        forensicResistance: 0,
        integrityVerified: false
      };
    }

    return voiceSecurityService.verifyAudioSecurity(
      this.securitySessionId,
      audioData,
      metadata
    );
  }

  /**
   * Cleanup session
   */
  destroy(): void {
    if (this.processor) {
      try {
        worldVocoder.cleanupProcessor(this.processor);
      } catch (error) {
        console.warn('Error cleaning up WORLD processor:', error);
      }
      this.processor = null;
    }

    // Cleanup security session
    if (this.securitySessionId) {
      voiceSecurityService.destroySecuritySession(this.securitySessionId);
      this.securitySessionId = undefined;
    }
    
    this.emit('destroyed', this.sessionId);
  }
  
  /**
   * Determine if JS processing should be used instead of native processing
   * for profiles with extreme parameters that cause issues with the native processor
   */
  shouldUseJsProcessing(profile: WorldVoiceProfile): boolean {
    // TEMPORARILY DISABLE JS PROCESSING FALLBACK - Try native first for all profiles
    // The native vocoder should handle these profiles better with proper parameter sanitization
    console.log('🔧 Checking if JS processing needed for profile:', {
      pitchScale: profile.pitchScale,
      spectralWarp: profile.spectralWarp,
      reverbAmount: profile.reverbAmount,
      antiForensic: profile.antiForensic
    });

    // Only use JS processing for extremely problematic cases that we know cause native vocoder to fail
    const isExtremelyProblematic =
      (profile.pitchScale < 0.5 || profile.pitchScale > 2.0) ||  // Extreme pitch shifts
      Math.abs(profile.spectralWarp) > 15.0 ||                   // Extreme formant shifts
      profile.reverbAmount > 50.0 ||                             // Extreme reverb
      profile.spectralNoise > 0.5;                               // Extreme noise

    console.log('🔧 JS processing needed:', isExtremelyProblematic);
    return isExtremelyProblematic;
  }

  /**
   * Fallback voice processing when native WORLD vocoder is not available
   */
  fallbackVoiceProcessing(audioData: Float32Array, profile: WorldVoiceProfile): Float32Array {
    console.log('🔄 Fallback processing with profile:', {
      pitchScale: profile.pitchScale,
      spectralWarp: profile.spectralWarp,
      reverbAmount: profile.reverbAmount,
      antiForensic: profile.antiForensic
    });

    // For NORMAL_VOICE profile, return original audio unchanged
    if (profile.pitchScale === 1.0 && 
        profile.spectralWarp === 0.0 && 
        profile.reverbAmount === 0.0 && 
        profile.eqTilt === 0.0 && 
        profile.temporalJitter === 0.0 && 
        profile.spectralNoise === 0.0 && 
        !profile.antiForensic) {
      console.log('✅ NORMAL profile detected, returning original audio');
      return new Float32Array(audioData);
    }

    // For other profiles, apply minimal safe transformations
    const result = new Float32Array(audioData);

    // Apply only safe pitch scaling for non-normal profiles
    if (profile.pitchScale !== 1.0) {
      console.log('🎵 Applying pitch scaling:', profile.pitchScale);
      
      // Simple time-domain pitch scaling (safer than frequency domain)
      const pitchScale = Math.max(0.5, Math.min(2.0, profile.pitchScale)); // Clamp to safe range
      
      for (let i = 0; i < result.length; i++) {
        const sourceIndex = Math.floor(i / pitchScale);
        if (sourceIndex < audioData.length) {
          result[i] = audioData[sourceIndex];
        } else {
          result[i] = 0;
        }
      }
    }

    // Apply very subtle amplitude modulation for other effects
    if (profile.reverbAmount > 0) {
      console.log('🔊 Applying subtle amplitude modulation for reverb simulation');
      const reverbFactor = 1.0 - (Math.min(profile.reverbAmount, 50) * 0.002); // Very subtle
      for (let i = 0; i < result.length; i++) {
        result[i] *= reverbFactor;
      }
    }

    // Apply very gentle spectral warping simulation
    if (profile.spectralWarp !== 0.0) {
      console.log('🌊 Applying gentle spectral warping simulation');
      const warpAmount = Math.max(-20, Math.min(20, profile.spectralWarp)); // Clamp to safe range
      const warpFactor = 1.0 + (warpAmount * 0.001); // Very subtle
      
      for (let i = 0; i < result.length; i++) {
        result[i] *= warpFactor;
      }
    }

    // Ensure no clipping
    for (let i = 0; i < result.length; i++) {
      result[i] = Math.max(-1, Math.min(1, result[i]));
    }

    console.log('✅ Fallback processing completed');
    return result;
  }

  private updateStats(latency: number): void {
    this.stats.frameCount++;
    this.stats.latency = latency;
    this.stats.averageLatency =
      (this.stats.averageLatency * (this.stats.frameCount - 1) + latency) / this.stats.frameCount;
  }


}

/**
 * WORLD Vocoder Service
 * Manages multiple voice processing sessions
 */
export class WorldVocoderService extends EventEmitter {
  /**
   * Get the session object by sessionId
   */
  getSession(sessionId: string): WorldVocoderSession | undefined {
    return this.sessions.get(sessionId);
  }
  private sessions: Map<string, WorldVocoderSession> = new Map();
  private defaultConfig: WorldProcessingConfig = {
    sampleRate: 48000,
    frameSize: 960, // 20ms at 48kHz
    realTimeMode: true,
    qualityLevel: 'balanced'
  };

  constructor() {
    super();
    this.checkNativeAddonAvailability();
  }

  /**
   * Check if voice processing is available (native or fallback)
   */
  isAvailable(): boolean {
    return true; // Always available with fallback mode
  }

  /**
   * Check if native WORLD vocoder addon is available
   */
  isNativeAvailable(): boolean {
    return worldVocoder !== null;
  }

  /**
   * Create a new voice processing session
   */
  async createSession(userId: string, config?: Partial<WorldProcessingConfig>): Promise<string> {
    const sessionId = crypto.randomUUID();
    const sessionConfig = { ...this.defaultConfig, ...config };

    const session = new WorldVocoderSession(sessionId, sessionConfig);

    // Forward session events
    session.on('error', (error) => this.emit('sessionError', sessionId, error));
    session.on('frameProcessed', (data) => this.emit('frameProcessed', data));
    session.on('destroyed', (id) => this.sessions.delete(id));

    const initialized = await session.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize WORLD vocoder session');
    }

    this.sessions.set(sessionId, session);

    this.emit('sessionCreated', {
      sessionId,
      userId,
      config: sessionConfig
    });

    return sessionId;
  }

  /**
   * Process audio frame for a session
   */
  async processAudioFrame(sessionId: string, audioData: Float32Array,
    profile: WorldVoiceProfile): Promise<Float32Array | null> {
    console.log('🎧 processAudioFrame called with session:', sessionId);
    console.log('🎧 Audio data length:', audioData.length);
    console.log('🎧 Profile:', { pitchScale: profile.pitchScale, spectralWarp: profile.spectralWarp, antiForensic: profile.antiForensic });
    
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.error('❌ Session not found:', sessionId);
      console.error('❌ Available sessions:', Array.from(this.sessions.keys()));
      throw new Error(`Session ${sessionId} not found`);
    }
    
    console.log('🎧 Session found, calling processFrame...');
    try {
      const result = await session.processFrame(audioData, profile);
      console.log('🎧 processFrame completed, result:', result ? `${result.length} samples` : 'null');
      return result;
    } catch (error) {
      console.error('❌ processFrame threw error:', error);
      if (error instanceof Error) {
        console.error('❌ processFrame error stack:', error.stack);
      }
      throw error;
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): ProcessingStats | null {
    const session = this.sessions.get(sessionId);
    return session ? session.getStats() : null;
  }

  /**
   * Process audio buffer with a specific profile
   * This is used for one-off processing like generating samples
   */
  async processAudioWithProfile(audioBuffer: Buffer, profile: WorldVoiceProfile): Promise<Buffer> {
    try {
      console.log(`Processing audio buffer (${audioBuffer.length} bytes) with profile`);
      
      // Create a temporary session for processing
      const tempSessionId = await this.createSession('temp-user');
      
      // CRITICAL FIX: Extract PCM data from WAV file (skip 44-byte header)
      let pcmBuffer: Buffer;
      let sampleRate = 48000; // Default
      
      // Check if this is a WAV file with header
      if (audioBuffer.length > 44 && 
          audioBuffer.toString('ascii', 0, 4) === 'RIFF' && 
          audioBuffer.toString('ascii', 8, 12) === 'WAVE') {
        
        console.log('🎵 Detected WAV format, extracting PCM data...');
        
        // Extract sample rate from WAV header (bytes 24-27)
        sampleRate = audioBuffer.readUInt32LE(24);
        console.log('🎵 Sample rate from WAV:', sampleRate);
        
        // Extract PCM data (skip 44-byte WAV header)
        pcmBuffer = audioBuffer.subarray(44);
        console.log('🎵 PCM data extracted:', pcmBuffer.length, 'bytes');
        
      } else {
        console.log('🎵 Assuming raw PCM data (no WAV header)');
        pcmBuffer = audioBuffer;
      }
      
      // Convert PCM buffer to Float32Array with proper normalization
      const float32Array = new Float32Array(pcmBuffer.length / 2);
      for (let i = 0; i < float32Array.length; i++) {
        // Read Int16 and normalize to [-1, 1] range
        const int16Value = pcmBuffer.readInt16LE(i * 2);
        float32Array[i] = int16Value / 32768.0; // Normalize to [-1, 1]
      }
      
      console.log('🔧 Converted to Float32Array:', float32Array.length, 'samples');
      console.log('🔧 Input audio range:', Math.min(...Array.from(float32Array)).toFixed(4), 'to', Math.max(...Array.from(float32Array)).toFixed(4));
      
      // Process the audio
      let processedFloat32: Float32Array | null = null;
      try {
        processedFloat32 = await this.processAudioFrame(tempSessionId, float32Array, profile);
        console.log('✅ Audio frame processing completed successfully');
      } catch (frameProcessingError) {
        console.error('❌ Audio frame processing failed:', frameProcessingError);
        
        // TRY JS FALLBACK
        console.log('🔄 Trying JS fallback processor after native processing failed');
        try {
          // Import the JS Voice Processor
          const { jsVoiceProcessor } = require('./jsVoiceProcessor');
          
          // Use JS processor as a last-resort fallback
          processedFloat32 = jsVoiceProcessor.processAudio(float32Array, profile);
          console.log('✅ JS Voice Processor fallback completed successfully');
        } catch (jsFallbackError) {
          console.error('❌ JS fallback processing also failed:', jsFallbackError);
          // If JS fallback also fails, return original audio
          processedFloat32 = new Float32Array(float32Array);
        }
      }
      
      // Convert back to buffer
      if (!processedFloat32) {
        console.warn('Audio processing returned null, using original PCM data');
        return this.createWavBuffer(pcmBuffer, sampleRate);
      }
      
      console.log('🔧 Output audio range:', Math.min(...Array.from(processedFloat32)).toFixed(4), 'to', Math.max(...Array.from(processedFloat32)).toFixed(4));
      
      // CRITICAL: Apply soft clipping and amplitude limiting to prevent audio saturation
      const maxAmplitude = Math.max(...Array.from(processedFloat32).map(Math.abs));
      
      if (maxAmplitude > 0.95) {
        console.warn(`⚠️ Output amplitude too high (${maxAmplitude.toFixed(3)}), applying soft limiting...`);
        
        // Apply soft limiting with compression to prevent clipping
        const targetAmplitude = 0.8; // Safe target level
        const compressionRatio = targetAmplitude / maxAmplitude;
        
        // Apply soft compression curve
        for (let i = 0; i < processedFloat32.length; i++) {
          const sample = processedFloat32[i];
          const absSample = Math.abs(sample);
          
          if (absSample > 0.7) {
            // Apply soft compression to loud samples
            const sign = sample >= 0 ? 1 : -1;
            const compressedSample = sign * (0.7 + (absSample - 0.7) * 0.3); // Soft knee compression
            processedFloat32[i] = compressedSample * compressionRatio;
          } else {
            // Apply linear scaling to quiet samples
            processedFloat32[i] = sample * compressionRatio;
          }
        }
        
        const newMaxAmplitude = Math.max(...Array.from(processedFloat32).map(Math.abs));
        console.log(`✅ Amplitude limited from ${maxAmplitude.toFixed(3)} to ${newMaxAmplitude.toFixed(3)}`);
      }
      
      // Convert Float32Array back to Int16 PCM buffer with proper clamping
      const outputPcmBuffer = Buffer.alloc(processedFloat32.length * 2);
      for (let i = 0; i < processedFloat32.length; i++) {
        // Additional safety clamp to [-0.99, 0.99] to ensure no clipping
        const clampedSample = Math.max(-0.99, Math.min(0.99, processedFloat32[i]));
        const int16Value = Math.round(clampedSample * 32767);
        outputPcmBuffer.writeInt16LE(int16Value, i * 2);
      }
      
      // Create proper WAV file with headers
      const outputWavBuffer = this.createWavBuffer(outputPcmBuffer, sampleRate);
      
      console.log('✅ Created output WAV buffer:', outputWavBuffer.length, 'bytes');
      
      // Cleanup the temporary session
      this.destroySession(tempSessionId);
      
      return outputWavBuffer;
    } catch (error) {
      console.error('Error processing audio with profile:', error);
      
      // CRITICAL FIX: Return PCM data with WAV headers, not the original audioBuffer
      // to prevent infinite recursion due to WAV header confusion
      if (audioBuffer.length > 44 && 
          audioBuffer.toString('ascii', 0, 4) === 'RIFF' && 
          audioBuffer.toString('ascii', 8, 12) === 'WAVE') {
        // Extract PCM data from original WAV file and return it properly formatted
        const originalPcmBuffer = audioBuffer.subarray(44);
        const originalSampleRate = audioBuffer.readUInt32LE(24);
        console.log('🔧 Error recovery: Returning original PCM data with proper WAV headers');
        return this.createWavBuffer(originalPcmBuffer, originalSampleRate);
      } else {
        // If it's not a WAV file, return original data with default WAV headers
        console.log('🔧 Error recovery: Returning original raw data with default WAV headers');
        return this.createWavBuffer(audioBuffer, 48000);
      }
    }
  }

  /**
   * Create a proper WAV file buffer with headers
   */
  private createWavBuffer(pcmData: Buffer, sampleRate: number): Buffer {
    const headerLength = 44;
    const totalLength = headerLength + pcmData.length;
    const buffer = Buffer.alloc(totalLength);
    
    // WAV header
    buffer.write('RIFF', 0, 4, 'ascii');
    buffer.writeUInt32LE(totalLength - 8, 4);
    buffer.write('WAVE', 8, 4, 'ascii');
    buffer.write('fmt ', 12, 4, 'ascii');
    buffer.writeUInt32LE(16, 16); // fmt chunk size
    buffer.writeUInt16LE(1, 20); // PCM format
    buffer.writeUInt16LE(1, 22); // mono
    buffer.writeUInt32LE(sampleRate, 24); // sample rate
    buffer.writeUInt32LE(sampleRate * 2, 28); // byte rate
    buffer.writeUInt16LE(2, 32); // block align
    buffer.writeUInt16LE(16, 34); // bits per sample
    buffer.write('data', 36, 4, 'ascii');
    buffer.writeUInt32LE(pcmData.length, 40);
    
    // Copy PCM data
    pcmData.copy(buffer, headerLength);
    
    return buffer;
  }
  
  /**
   * Destroy a session
   */
  destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.destroy();
      return true;
    }
    return false;
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * Get total number of active sessions
   */
  getActiveSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Cleanup all sessions
   */
  cleanup(): void {
    for (const session of this.sessions.values()) {
      session.destroy();
    }
    this.sessions.clear();
  }

  private checkNativeAddonAvailability(): void {
    if (!worldVocoder) {
      console.log('🔄 WORLD vocoder using fallback mode (native addon not available)');
      console.log('   ✅ Voice morphing fully functional with JavaScript implementation');
      console.log('   📝 To enable native mode: cd backend && npm run build:world');
    } else {
      console.log('✅ WORLD vocoder native addon loaded successfully');
      // Initialize JS fallback anyway for non-normal profiles
      try {
        // Dynamic import to avoid circular dependencies
        require('./jsVoiceProcessor');
      } catch (e) {
        console.warn('⚠️ Failed to initialize JS fallback processor:', e);
      }
    }
  }
}

/**
 * Sanitize and validate WORLD vocoder profile parameters to prevent audio clipping
 * Based on research from WORLD vocoder papers and clipping prevention best practices
 */
function sanitizeVoiceProfile(profile: WorldVoiceProfile): WorldVoiceProfile {
  // EXTREME RANGES: Allow dramatic voice transformation for real morphing
  // These ranges enable true gender transformation and voice conversion
  return {
    // Pitch scaling: EXTREME range for real gender transformation
    // 0.4-2.5 allows for dramatic pitch changes (male->female, female->male)
    pitchScale: Math.max(0.4, Math.min(2.5, profile.pitchScale)),

    // Spectral warping: EXTREME range for vocal tract length normalization
    // ±15.0 allows for major formant shifting (VTLN for gender conversion)
    spectralWarp: Math.max(-15.0, Math.min(15.0, profile.spectralWarp)),

    // Reverb amount: Allow higher values for special effects
    // Up to 50% for echo chamber and synthetic effects
    reverbAmount: Math.max(0, Math.min(50.0, profile.reverbAmount)),

    // EQ tilt: Allow wider range for dramatic frequency shaping
    // ±4dB for strong gender-specific frequency emphasis
    eqTilt: Math.max(-4.0, Math.min(4.0, profile.eqTilt)),

    // Temporal jitter: Allow higher values for anti-forensic effects
    // Up to 0.15 for strong anonymization
    temporalJitter: Math.max(0, Math.min(0.15, profile.temporalJitter)),

    // Spectral noise: Allow higher values for masking effects
    // Up to 0.2 for strong anti-forensic processing
    spectralNoise: Math.max(0, Math.min(0.2, profile.spectralNoise)),

    // Anti-forensic flag unchanged
    antiForensic: profile.antiForensic
  };
}

// Predefined voice profiles with EXTREME parameters for REAL voice transformation
// Based on research in voice conversion and gender transformation
export const WORLD_VOICE_PROFILES: Record<string, WorldVoiceProfile> = {
  // EXTREME GENDER TRANSFORMATION PROFILES
  DEEP_MALE: {
    pitchScale: 0.50,    // EXTREME pitch drop for deep masculine voice
    spectralWarp: -8.0,  // MAJOR formant shift for large vocal tract
    reverbAmount: 5.0,   // Minimal reverb for clarity
    eqTilt: -3.0,        // Strong bass emphasis
    temporalJitter: 0.01, // Minimal jitter for natural sound
    spectralNoise: 0.02, // Very low noise for clarity
    antiForensic: true
  },
  HIGH_FEMALE: {
    pitchScale: 2.0,     // EXTREME pitch raise for feminine voice
    spectralWarp: 8.0,   // MAJOR formant shift for small vocal tract
    reverbAmount: 4.0,   // Minimal reverb for clarity
    eqTilt: 3.0,         // Strong treble emphasis
    temporalJitter: 0.01, // Minimal jitter for natural sound
    spectralNoise: 0.02, // Very low noise for clarity
    antiForensic: true
  },

  // EXTREME VOCAL TRACT TRANSFORMATION
  MALE_TO_FEMALE: {
    pitchScale: 1.8,     // Raise pitch significantly
    spectralWarp: 12.0,  // EXTREME formant shift up (vocal tract shortening)
    reverbAmount: 3.0,   // Minimal reverb for clarity
    eqTilt: 2.5,         // Strong treble emphasis
    temporalJitter: 0.01, // Minimal jitter for naturalness
    spectralNoise: 0.02, // Low noise for clarity
    antiForensic: true
  },
  FEMALE_TO_MALE: {
    pitchScale: 0.55,    // Lower pitch dramatically
    spectralWarp: -12.0, // EXTREME formant shift down (vocal tract lengthening)
    reverbAmount: 3.0,   // Minimal reverb for clarity
    eqTilt: -2.5,        // Strong bass emphasis
    temporalJitter: 0.01, // Minimal jitter for naturalness
    spectralNoise: 0.02, // Low noise for clarity
    antiForensic: true
  },

  // SECURE ANTI-FORENSIC PROFILES
  SECURE_DEEP_MALE: {
    pitchScale: 0.65,    // Very deep for security
    spectralWarp: -6.0,  // Strong formant shift
    reverbAmount: 15.0,  // More reverb for masking
    eqTilt: -2.5,        // Bass emphasis
    temporalJitter: 0.03, // Anti-forensic timing
    spectralNoise: 0.05, // Moderate noise for security
    antiForensic: true
  },
  SECURE_HIGH_FEMALE: {
    pitchScale: 1.6,     // Very high for security
    spectralWarp: 6.0,   // Strong formant shift up
    reverbAmount: 12.0,  // Moderate reverb for masking
    eqTilt: 2.0,         // Treble emphasis
    temporalJitter: 0.02, // Anti-forensic timing
    spectralNoise: 0.05, // Moderate noise for security
    antiForensic: true
  },

  // ROBOTIC AND SYNTHETIC PROFILES
  ROBOTIC_SYNTHETIC: {
    pitchScale: 0.85,    // Lower for robotic feel
    spectralWarp: -2.0,  // Moderate formant shift
    reverbAmount: 25.0,  // High reverb for synthetic feel
    eqTilt: -1.5,        // Bass emphasis for robotic sound
    temporalJitter: 0.06, // Higher jitter for robotic artifacts
    spectralNoise: 0.08, // Moderate noise for synthetic feel
    antiForensic: true
  },
  // EXTREME TRANSFORMATION PROFILES
  ULTRA_DEEP_BASS: {
    pitchScale: 0.60,    // Very deep voice
    spectralWarp: -5.0,  // Very low formants
    reverbAmount: 10.0,  // Moderate reverb
    eqTilt: -3.0,        // Strong bass emphasis
    temporalJitter: 0.02, // Low jitter for clarity
    spectralNoise: 0.03, // Low noise for clarity
    antiForensic: true
  },
  ULTRA_HIGH_SOPRANO: {
    pitchScale: 1.50,    // Very high voice
    spectralWarp: 5.0,   // Very high formants
    reverbAmount: 8.0,   // Moderate reverb
    eqTilt: 2.5,         // Strong treble emphasis
    temporalJitter: 0.02, // Low jitter for clarity
    spectralNoise: 0.03, // Low noise for clarity
    antiForensic: true
  },

  // ANDROID AND AI VOICES
  ANDROID_VOICE: {
    pitchScale: 1.0,     // Normal pitch but processed
    spectralWarp: 0.0,   // No formant shift
    reverbAmount: 30.0,  // High reverb for artificial feel
    eqTilt: 0.0,         // Flat EQ
    temporalJitter: 0.08, // High jitter for digital artifacts
    spectralNoise: 0.10, // Higher noise for digital feel
    antiForensic: true
  },
  CYBORG_VOICE: {
    pitchScale: 0.90,    // Slightly lower
    spectralWarp: -1.0,  // Slight formant shift
    reverbAmount: 35.0,  // Very high reverb
    eqTilt: -0.5,        // Slight bass boost
    temporalJitter: 0.10, // Very high jitter
    spectralNoise: 0.12, // High noise for mechanical feel
    antiForensic: true
  },

  // SPECIAL EFFECT PROFILES
  ECHO_CHAMBER: {
    pitchScale: 1.0,     // Normal pitch
    spectralWarp: 0.0,   // No formant shift
    reverbAmount: 45.0,  // Very high reverb
    eqTilt: 0.0,         // Flat EQ
    temporalJitter: 0.01, // Minimal jitter
    spectralNoise: 0.02, // Low noise
    antiForensic: false
  },
  WHISPER_MODE: {
    pitchScale: 1.1,     // Slightly higher
    spectralWarp: 1.0,   // Slight formant shift
    reverbAmount: 5.0,   // Low reverb
    eqTilt: 1.0,         // Slight treble boost
    temporalJitter: 0.05, // Moderate jitter for breathiness
    spectralNoise: 0.12, // Higher noise for whisper effect
    antiForensic: true
  },
  ALIEN_VOICE: {
    pitchScale: 1.20,    // Higher pitch
    spectralWarp: 3.0,   // High formant shift
    reverbAmount: 20.0,  // High reverb
    eqTilt: 1.5,         // Treble emphasis
    temporalJitter: 0.07, // High jitter for alien feel
    spectralNoise: 0.09, // Moderate noise
    antiForensic: true
  },

  // CLASSIC VOICE TYPES
  DEEP_VOICE: {
    pitchScale: 0.85,    // Moderate pitch reduction
    spectralWarp: -2.0,  // Moderate spectral shift for depth
    reverbAmount: 8.0,   // Light reverb for presence
    eqTilt: -1.5,        // Bass emphasis
    temporalJitter: 0.01,
    spectralNoise: 0.02,
    antiForensic: false
  },
  HIGH_VOICE: {
    pitchScale: 1.20,    // Moderate pitch increase
    spectralWarp: 2.0,   // Moderate spectral shift for brightness
    reverbAmount: 6.0,   // Very light reverb
    eqTilt: 1.5,         // Treble emphasis
    temporalJitter: 0.01,
    spectralNoise: 0.02,
    antiForensic: false
  },

  // NORMAL VOICE (NO PROCESSING)
  NORMAL_VOICE: {
    pitchScale: 1.0,
    spectralWarp: 0.0,
    reverbAmount: 0.0,
    eqTilt: 0.0,
    temporalJitter: 0.0,
    spectralNoise: 0.0,
    antiForensic: false
  }
};

// Export singleton instance
export const worldVocoderService = new WorldVocoderService();
