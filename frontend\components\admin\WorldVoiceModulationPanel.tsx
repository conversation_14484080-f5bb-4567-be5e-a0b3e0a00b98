/**
 * WORLD Vocoder Voice Modulation Panel for Admin
 * Manages 20+ predefined voice profiles with real-time preview
 * Supports custom profile creation and user assignment
 */

import React, { useState, useEffect, useRef } from 'react';
import tokenManager from '../../utils/tokenManager';
import apiClient from '../../utils/apiClient';

export interface WorldVoiceProfile {
  name: string;
  description: string;
  parameters: {
    pitchScale: number;      // 0.7-1.3 (pitch modification)
    spectralWarp: number;    // -10% to +10% (formant shifting)
    reverbAmount: number;    // 0-50% (spatial distortion)
    eqTilt: number;         // -6dB to +6dB (frequency emphasis)
    temporalJitter: number; // Anti-forensic timing variation
    spectralNoise: number;  // Irreversible spectral masking
  };
  userType: 'all' | 'regular' | 'superuser';
  isCustom: boolean;
  audioSample?: string;    // Base64 encoded sample
}

interface WorldVoiceModulationPanelProps {
  userId: string;
  currentProfile: string;
  onProfileChange: (profileName: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const WorldVoiceModulationPanel: React.FC<WorldVoiceModulationPanelProps> = ({
  userId,
  currentProfile,
  onProfileChange,
  isOpen,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'profiles' | 'custom' | 'samples'>('profiles');
  const [profiles, setProfiles] = useState<WorldVoiceProfile[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<WorldVoiceProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [worldVocoderEnabled, setWorldVocoderEnabled] = useState(false);
  const [userType, setUserType] = useState<'regular' | 'superuser'>('regular');
  
  // Custom profile creation state
  const [customProfile, setCustomProfile] = useState<WorldVoiceProfile>({
    name: '',
    description: '',
    parameters: {
      pitchScale: 1.0,
      spectralWarp: 0.0,
      reverbAmount: 0.0,
      eqTilt: 0.0,
      temporalJitter: 0.0,
      spectralNoise: 0.0
    },
    userType: 'all',
    isCustom: true
  });

  // Audio testing state
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentlyPlayingProfile, setCurrentlyPlayingProfile] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('');
  const [sampleUrls, setSampleUrls] = useState<Record<string, string>>({});
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (isOpen) {
      fetchVoiceProfiles();
    }
  }, [isOpen]);

  const fetchVoiceProfiles = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get('/api/voice/realtime/profiles');
      
      if (response.data.success) {
        setProfiles(response.data.profiles);
        setWorldVocoderEnabled(response.data.worldVocoderEnabled);
        setUserType(response.data.userType);
        
        // Find and set current profile
        const current = response.data.profiles.find((p: any) => p.name === currentProfile);
        if (current) {
          setSelectedProfile(current);
        }
      } else {
        console.error('Failed to fetch voice profiles:', response.data.error);
      }
    } catch (error) {
      console.error('Failed to fetch voice profiles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileSelect = (profile: WorldVoiceProfile) => {
    setSelectedProfile(profile);
    onProfileChange(profile.name);
  };

  const playProfileSample = async (profile: WorldVoiceProfile) => {
    if (currentlyPlayingProfile === profile.name) {
      // Stop current playback
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      setCurrentlyPlayingProfile(null);
      return;
    }

    // Check if we have a generated sample for this profile
    if (sampleUrls[profile.name]) {
      try {
        if (audioRef.current) {
          audioRef.current.src = sampleUrls[profile.name];
          audioRef.current.load(); // Ensure audio is loaded
          await audioRef.current.play();
          setIsPlaying(true);
          setCurrentlyPlayingProfile(profile.name);
          setMessage(`Playing processed audio for ${profile.name}`);
        }
      } catch (playError) {
        console.error('Audio playback error:', playError);
        setMessage(`Failed to play audio for ${profile.name}. Try processing again.`);
      }
    } else if (profile.audioSample) {
      // Play pre-recorded sample
      try {
        if (audioRef.current) {
          audioRef.current.src = `data:audio/wav;base64,${profile.audioSample}`;
          audioRef.current.load(); // Ensure audio is loaded
          await audioRef.current.play();
          setIsPlaying(true);
          setCurrentlyPlayingProfile(profile.name);
          setMessage(`Playing sample for ${profile.name}`);
        }
      } catch (playError) {
        console.error('Audio playback error:', playError);
        setMessage(`Failed to play sample for ${profile.name}`);
      }
    } else {
      // Generate sample using test audio if available
      if (audioFile) {
        await generateProfileSample(profile);
      } else {
        setMessage('Please upload an audio file first in the Samples tab to test this profile');
      }
    }
  };

  const generateProfileSample = async (profile: WorldVoiceProfile) => {
    if (!audioFile) {
      setMessage('Please select an audio file first');
      return;
    }

    try {
      setIsLoading(true);
      setMessage('');

      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileName', profile.name);

      console.log(`Testing audio with profile: ${profile.name}`);

      const response = await apiClient.post('/api/voice/realtime/test-profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob',
      });

      console.log('Response received:', response.status, response.headers['content-type']);

      // Check if response is audio
      if (response.headers['content-type']?.includes('audio')) {
        // Create audio URL and store it
        const audioBlob = new Blob([response.data], { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        
        // Store the URL for playback
        setSampleUrls(prev => ({
          ...prev,
          [profile.name]: audioUrl
        }));

        setMessage(`Audio processed successfully with ${profile.name} profile - Click Play button to hear result`);
        
        // Auto-play the generated sample
        try {
          if (audioRef.current) {
            audioRef.current.src = audioUrl;
            audioRef.current.load();
            await audioRef.current.play();
            setIsPlaying(true);
            setCurrentlyPlayingProfile(profile.name);
          }
        } catch (playError) {
          console.log('Audio autoplay prevented:', playError);
          // Silent fail for autoplay - user can click play manually
        }
      } else {
        // Handle error response
        const text = await response.data.text();
        const errorData = JSON.parse(text);
        setMessage(`Failed to process audio: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('Error testing profile:', error);
      if (error.response?.status === 401) {
        setMessage('Authentication failed. Please check your admin access.');
      } else if (error.response?.status === 400) {
        try {
          const errorText = await error.response.data.text();
          const errorData = JSON.parse(errorText);
          if (errorData.availableProfiles) {
            setMessage(`Profile "${profile.name}" not found. Available profiles: ${errorData.availableProfiles.join(', ')}`);
          } else {
            setMessage(`Bad request: ${errorData.error || 'Invalid request'}`);
          }
        } catch (parseError) {
          setMessage(`Profile "${profile.name}" not found on server`);
        }
      } else if (error.response?.status === 404) {
        setMessage(`Profile "${profile.name}" not found on server`);
      } else {
        setMessage(`Failed to process audio with ${profile.name}: ${error.response?.data?.error || error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createCustomProfile = async () => {
    if (!customProfile.name.trim()) {
      setMessage('Please enter a profile name');
      return;
    }

    try {
      setIsLoading(true);
      setMessage('Creating custom profile...');

      const response = await apiClient.backend.post('/api/voice/profiles/custom', {
        name: customProfile.name,
        description: customProfile.description,
        parameters: customProfile.parameters,
        userType: customProfile.userType,
        isCustom: true
      });

      if (response.data.success) {
        setMessage('Custom profile created successfully!');

        // Reset form
        setCustomProfile({
          name: '',
          description: '',
          parameters: {
            pitchScale: 1.0,
            spectralWarp: 0.0,
            reverbAmount: 0.0,
            eqTilt: 0.0,
            temporalJitter: 0.0,
            spectralNoise: 0.0
          },
          userType: 'all',
          isCustom: true
        });

        // Refresh profiles list
        await fetchProfiles();
        setActiveTab('profiles');
      } else {
        throw new Error(response.data.error || 'Failed to create profile');
      }
    } catch (error: any) {
      console.error('Failed to create custom profile:', error);
      setMessage(`Failed to create profile: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testProfile = async (profile: WorldVoiceProfile) => {
    if (!audioFile) {
      setMessage('Please upload an audio file first to test the profile');
      return;
    }

    try {
      setIsLoading(true);
      setMessage(`Testing profile: ${profile.name}...`);

      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileData', JSON.stringify(profile));

      const response = await apiClient.backend.post('/api/voice/realtime/test-custom-profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob'
      });

      if (response.data) {
        const audioBlob = new Blob([response.data], { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          audioRef.current.load();
          await audioRef.current.play();
          setIsPlaying(true);
          setCurrentlyPlayingProfile(profile.name);
          setMessage(`Playing test sample for ${profile.name}`);
        }
      }
    } catch (error: any) {
      console.error('Error testing profile:', error);
      setMessage(`Failed to test profile: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderProfileCard = (profile: WorldVoiceProfile) => {
    const isSelected = selectedProfile?.name === profile.name;
    const isCurrentlyPlaying = currentlyPlayingProfile === profile.name && isPlaying;
    
    return (
      <div
        key={profile.name}
        className={`p-4 border rounded-lg cursor-pointer transition-all ${
          isSelected 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => handleProfileSelect(profile)}
      >
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-gray-900">{profile.name}</h3>
          <div className="flex space-x-2">
            {profile.userType === 'regular' && (
              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                Regular Only
              </span>
            )}
            {profile.isCustom && (
              <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                Custom
              </span>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                playProfileSample(profile);
              }}
              className="p-1 text-gray-500 hover:text-blue-600"
              disabled={isLoading}
              title={sampleUrls[profile.name] ? 'Play processed audio' : (profile.audioSample ? 'Play sample' : 'Test profile')}
            >
              {isCurrentlyPlaying ? (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
        </div>
        
        <p className="text-sm text-gray-600 mb-3">{profile.description}</p>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-500">Pitch:</span> {profile.parameters.pitchScale}x
          </div>
          <div>
            <span className="text-gray-500">Spectral:</span> {profile.parameters.spectralWarp}%
          </div>
          <div>
            <span className="text-gray-500">Reverb:</span> {profile.parameters.reverbAmount}%
          </div>
          <div>
            <span className="text-gray-500">EQ Tilt:</span> {profile.parameters.eqTilt}dB
          </div>
        </div>
        
        {profile.parameters.temporalJitter > 0 && (
          <div className="mt-2 flex items-center text-xs text-orange-600">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Anti-forensic enabled
          </div>
        )}
      </div>
    );
  };

  const renderCustomProfileEditor = () => (
    <div className="space-y-6">
      <div className="text-sm text-gray-600">
        Create custom voice profiles with advanced parameters for specific users or use cases.
      </div>

      {/* Profile Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Profile Name *
          </label>
          <input
            type="text"
            value={customProfile.name}
            onChange={(e) => setCustomProfile({ ...customProfile, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
            placeholder="Enter profile name"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <input
            type="text"
            value={customProfile.description}
            onChange={(e) => setCustomProfile({ ...customProfile, description: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
            placeholder="Profile description"
          />
        </div>
      </div>

      {/* Voice Parameters */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-md font-semibold text-gray-900 mb-4">Voice Parameters</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pitch Scale */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pitch Scale: {customProfile.parameters.pitchScale.toFixed(2)}
            </label>
            <input
              type="range"
              min="0.7"
              max="1.3"
              step="0.01"
              value={customProfile.parameters.pitchScale}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, pitchScale: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">0.7 (lower) - 1.3 (higher)</div>
          </div>

          {/* Spectral Warp */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spectral Warp: {customProfile.parameters.spectralWarp.toFixed(1)}
            </label>
            <input
              type="range"
              min="-10"
              max="10"
              step="0.1"
              value={customProfile.parameters.spectralWarp}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, spectralWarp: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">-10 (formant down) - +10 (formant up)</div>
          </div>

          {/* Reverb Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reverb Amount: {customProfile.parameters.reverbAmount.toFixed(1)}%
            </label>
            <input
              type="range"
              min="0"
              max="50"
              step="0.5"
              value={customProfile.parameters.reverbAmount}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, reverbAmount: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">0% (dry) - 50% (wet)</div>
          </div>

          {/* EQ Tilt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              EQ Tilt: {customProfile.parameters.eqTilt.toFixed(1)}dB
            </label>
            <input
              type="range"
              min="-6"
              max="6"
              step="0.1"
              value={customProfile.parameters.eqTilt}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, eqTilt: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">-6dB (bass) - +6dB (treble)</div>
          </div>

          {/* Temporal Jitter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Temporal Jitter: {(customProfile.parameters.temporalJitter * 100).toFixed(1)}%
            </label>
            <input
              type="range"
              min="0"
              max="0.1"
              step="0.001"
              value={customProfile.parameters.temporalJitter}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, temporalJitter: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">0% (none) - 10% (high anti-forensic)</div>
          </div>

          {/* Spectral Noise */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spectral Noise: {(customProfile.parameters.spectralNoise * 100).toFixed(1)}%
            </label>
            <input
              type="range"
              min="0"
              max="0.3"
              step="0.001"
              value={customProfile.parameters.spectralNoise}
              onChange={(e) => setCustomProfile({
                ...customProfile,
                parameters: { ...customProfile.parameters, spectralNoise: parseFloat(e.target.value) }
              })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="text-xs text-gray-500 mt-1">0% (clean) - 30% (high masking)</div>
          </div>
        </div>
      </div>

      {/* User Type Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Available To
        </label>
        <select
          value={customProfile.userType}
          onChange={(e) => setCustomProfile({ ...customProfile, userType: e.target.value as 'all' | 'regular' | 'superuser' })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
        >
          <option value="all">All Users</option>
          <option value="regular">Regular Users Only</option>
          <option value="superuser">Superuser Only</option>
        </select>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <button
          onClick={() => {
            // Reset to default values
            setCustomProfile({
              name: '',
              description: '',
              parameters: {
                pitchScale: 1.0,
                spectralWarp: 0.0,
                reverbAmount: 0.0,
                eqTilt: 0.0,
                temporalJitter: 0.0,
                spectralNoise: 0.0
              },
              userType: 'all',
              isCustom: true
            });
          }}
          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          Reset
        </button>

        <div className="space-x-2">
          <button
            onClick={() => testProfile(customProfile)}
            disabled={isLoading || !customProfile.name.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            Test Profile
          </button>
          <button
            onClick={createCustomProfile}
            disabled={isLoading || !customProfile.name.trim()}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {isLoading ? 'Creating...' : 'Create Profile'}
          </button>
        </div>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">WORLD Voice Modulation</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {!worldVocoderEnabled && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 text-yellow-800 text-sm rounded-md">
              <div className="flex">
                <svg className="flex-shrink-0 w-4 h-4 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div className="ml-2">
                  <p className="font-medium">WORLD vocoder not available</p>
                  <p className="text-xs mt-1">Using fallback implementation for voice processing</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex-shrink-0 px-6 py-3 border-b border-gray-100">
          <div className="flex space-x-1">
            {['profiles', 'custom', 'samples'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'profiles' && (
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <p className="ml-3 text-gray-600">Loading profiles...</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {profiles.map(renderProfileCard)}
                </div>
              )}
            </div>
          )}

          {activeTab === 'custom' && renderCustomProfileEditor()}

          {activeTab === 'samples' && (
            <div className="space-y-6">
              {/* Upload Section */}
              <div className="bg-gray-50 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-900 mb-3">
                  Upload Test Audio File
                </label>
                <input
                  type="file"
                  accept="audio/*"
                  onChange={(e) => setAudioFile(e.target.files?.[0] || null)}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                <p className="mt-2 text-xs text-gray-500">
                  Supported formats: WAV, MP3, M4A, OGG (Max size: 20MB)
                </p>
              </div>
              
              {audioFile && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <svg className="flex-shrink-0 w-5 h-5 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">File Ready for Processing</p>
                      <p className="text-sm text-green-700 mt-1">{audioFile.name}</p>
                      <p className="text-xs text-green-600 mt-1">
                        Size: {(audioFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {message && (
                <div className={`p-4 rounded-lg text-sm ${
                  message.includes('successfully') || message.includes('processed') 
                    ? 'bg-green-50 border border-green-200 text-green-800'
                    : message.includes('failed') || message.includes('error') || message.includes('Authentication')
                    ? 'bg-red-50 border border-red-200 text-red-800'
                    : 'bg-blue-50 border border-blue-200 text-blue-800'
                }`}>
                  <div className="flex">
                    <svg className="flex-shrink-0 w-4 h-4 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <p className="ml-2">{message}</p>
                  </div>
                </div>
              )}

              {audioFile && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Test Voice Profiles
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Select voice profiles to process your uploaded audio. Each profile applies different voice transformation settings.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {profiles.slice(0, 6).map((profile) => (
                      <div
                        key={profile.name}
                        className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors bg-white shadow-sm"
                      >
                        <div className="mb-3">
                          <h4 className="font-medium text-sm text-gray-900 mb-1">{profile.name}</h4>
                          <p className="text-xs text-gray-600 line-clamp-2">{profile.description}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-4">
                          <div>Pitch: {profile.parameters.pitchScale}×</div>
                          <div>Spectral: {profile.parameters.spectralWarp}%</div>
                        </div>
                        
                        <div className="flex flex-col space-y-2">
                          <button
                            onClick={() => generateProfileSample(profile)}
                            disabled={isLoading}
                            className="flex items-center justify-center space-x-2 w-full px-3 py-2 bg-blue-50 text-blue-700 rounded-md text-sm hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            {isLoading ? (
                              <>
                                <div className="w-4 h-4 border border-blue-300 border-t-blue-600 rounded-full animate-spin"></div>
                                <span>Processing...</span>
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                </svg>
                                <span>Process Audio</span>
                              </>
                            )}
                          </button>
                          
                          {sampleUrls[profile.name] && (
                            <button
                              onClick={() => playProfileSample(profile)}
                              className="flex items-center justify-center space-x-2 w-full px-3 py-2 bg-green-50 text-green-700 rounded-md text-sm hover:bg-green-100 transition-colors"
                              title="Play processed audio"
                            >
                              {currentlyPlayingProfile === profile.name && isPlaying ? (
                                <>
                                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                  </svg>
                                  <span>Playing...</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                  </svg>
                                  <span>Play Result</span>
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Audio Element */}
        <audio
          ref={audioRef}
          preload="none"
          onEnded={() => {
            setIsPlaying(false);
            setCurrentlyPlayingProfile(null);
            setMessage('Audio playback completed');
          }}
          onError={(e) => {
            console.error('Audio error:', e);
            setIsPlaying(false);
            setCurrentlyPlayingProfile(null);
            setMessage('Audio playback error - please try again');
          }}
          onLoadStart={() => {
            console.log('Audio loading started');
          }}
          onCanPlay={() => {
            console.log('Audio can play');
          }}
        />
      </div>
    </div>
  );
};

export default WorldVoiceModulationPanel;
